<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IHacpEmergencyTaskDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.HacpEmergencyTaskDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR" />
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR" />
        <result column="task_name" property="taskName" jdbcType="VARCHAR" />
        <result column="task_type" property="taskType" jdbcType="VARCHAR" />
        <result column="task_describe" property="taskDescribe" jdbcType="VARCHAR" />
        <result column="task_operator" property="taskOperator" jdbcType="VARCHAR" />
        <result column="operator" property="operator" jdbcType="VARCHAR" />
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="tm_smp" property="tmSmp" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.cmpay.hacp.entity.HacpEmergencyTaskDO" extends="BaseResultMap" >
        <result column="task_param" property="taskParam" jdbcType="LONGVARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, tenant_id, workspace_id, task_name, task_type, task_describe, task_operator, 
        operator, operator_name, create_time, update_time, status, tm_smp
    </sql>

    <sql id="Blob_Column_List" >
        task_param
    </sql>

    <select id="get" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from hacp_emergency_task
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="delete" parameterType="java.lang.Long" >
        delete from hacp_emergency_task
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.HacpEmergencyTaskDO" useGeneratedKeys="true" keyProperty="id" >
        insert into hacp_emergency_task
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="tenantId != null" >
                tenant_id,
            </if>
            <if test="workspaceId != null" >
                workspace_id,
            </if>
            <if test="taskName != null" >
                task_name,
            </if>
            <if test="taskType != null" >
                task_type,
            </if>
            <if test="taskDescribe != null" >
                task_describe,
            </if>
            <if test="taskOperator != null" >
                task_operator,
            </if>
            <if test="operator != null" >
                operator,
            </if>
            <if test="operatorName != null" >
                operator_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="tmSmp != null" >
                tm_smp,
            </if>
            <if test="taskParam != null" >
                task_param,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null" >
                #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskType != null" >
                #{taskType,jdbcType=VARCHAR},
            </if>
            <if test="taskDescribe != null" >
                #{taskDescribe,jdbcType=VARCHAR},
            </if>
            <if test="taskOperator != null" >
                #{taskOperator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null" >
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="taskParam != null" >
                #{taskParam,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.HacpEmergencyTaskDO" >
        update hacp_emergency_task
        <set >
            <if test="tenantId != null" >
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="workspaceId != null" >
                workspace_id = #{workspaceId,jdbcType=VARCHAR},
            </if>
            <if test="taskName != null" >
                task_name = #{taskName,jdbcType=VARCHAR},
            </if>
            <if test="taskType != null" >
                task_type = #{taskType,jdbcType=VARCHAR},
            </if>
            <if test="taskDescribe != null" >
                task_describe = #{taskDescribe,jdbcType=VARCHAR},
            </if>
            <if test="taskOperator != null" >
                task_operator = #{taskOperator,jdbcType=VARCHAR},
            </if>
            <if test="operator != null" >
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null" >
                operator_name = #{operatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="tmSmp != null" >
                tm_smp = #{tmSmp,jdbcType=VARCHAR},
            </if>
            <if test="taskParam != null" >
                task_param = #{taskParam,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cmpay.hacp.entity.HacpEmergencyTaskDO" >
        update hacp_emergency_task
        set tenant_id = #{tenantId,jdbcType=VARCHAR},
            workspace_id = #{workspaceId,jdbcType=VARCHAR},
            task_name = #{taskName,jdbcType=VARCHAR},
            task_type = #{taskType,jdbcType=VARCHAR},
            task_describe = #{taskDescribe,jdbcType=VARCHAR},
            task_operator = #{taskOperator,jdbcType=VARCHAR},
            operator = #{operator,jdbcType=VARCHAR},
            operator_name = #{operatorName,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            status = #{status,jdbcType=VARCHAR},
            tm_smp = #{tmSmp,jdbcType=VARCHAR},
            task_param = #{taskParam,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.HacpEmergencyTaskDO" >
        select 
        <include refid="Base_Column_List" />
        from hacp_emergency_task
        <where >
            <if test="id != null" >
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null" >
                and tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null" >
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="taskName != null" >
                and task_name = #{taskName,jdbcType=VARCHAR}
            </if>
            <if test="taskType != null" >
                and task_type = #{taskType,jdbcType=VARCHAR}
            </if>
            <if test="taskDescribe != null" >
                and task_describe = #{taskDescribe,jdbcType=VARCHAR}
            </if>
            <if test="taskOperator != null" >
                and task_operator = #{taskOperator,jdbcType=VARCHAR}
            </if>
            <if test="operator != null" >
                and operator = #{operator,jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null" >
                and operator_name = #{operatorName,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null" >
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null" >
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null" >
                and status = #{status,jdbcType=VARCHAR}
            </if>
            <if test="tmSmp != null" >
                and tm_smp = #{tmSmp,jdbcType=VARCHAR}
            </if>
            <if test="taskParam != null" >
                and task_param = #{taskParam,jdbcType=LONGVARCHAR}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>