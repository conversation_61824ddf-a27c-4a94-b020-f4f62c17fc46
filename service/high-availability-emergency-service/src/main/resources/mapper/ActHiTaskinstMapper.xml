<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IActHiTaskinstDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.ActHiTaskinstDO" >
        <id column="ID_" property="id" jdbcType="VARCHAR" />
        <result column="TASK_DEF_KEY_" property="taskDefKey" jdbcType="VARCHAR" />
        <result column="PROC_DEF_KEY_" property="procDefKey" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="ROOT_PROC_INST_ID_" property="rootProcInstId" jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="EXECUTION_ID_" property="executionId" jdbcType="VARCHAR" />
        <result column="CASE_DEF_KEY_" property="caseDefKey" jdbcType="VARCHAR" />
        <result column="CASE_DEF_ID_" property="caseDefId" jdbcType="VARCHAR" />
        <result column="CASE_INST_ID_" property="caseInstId" jdbcType="VARCHAR" />
        <result column="CASE_EXECUTION_ID_" property="caseExecutionId" jdbcType="VARCHAR" />
        <result column="ACT_INST_ID_" property="actInstId" jdbcType="VARCHAR" />
        <result column="NAME_" property="name" jdbcType="VARCHAR" />
        <result column="PARENT_TASK_ID_" property="parentTaskId" jdbcType="VARCHAR" />
        <result column="DESCRIPTION_" property="description" jdbcType="VARCHAR" />
        <result column="OWNER_" property="owner" jdbcType="VARCHAR" />
        <result column="ASSIGNEE_" property="assignee" jdbcType="VARCHAR" />
        <result column="START_TIME_" property="startTime" jdbcType="TIMESTAMP" />
        <result column="END_TIME_" property="endTime" jdbcType="TIMESTAMP" />
        <result column="DURATION_" property="duration" jdbcType="BIGINT" />
        <result column="DELETE_REASON_" property="deleteReason" jdbcType="VARCHAR" />
        <result column="PRIORITY_" property="priority" jdbcType="INTEGER" />
        <result column="DUE_DATE_" property="dueDate" jdbcType="TIMESTAMP" />
        <result column="FOLLOW_UP_DATE_" property="followUpDate" jdbcType="TIMESTAMP" />
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR" />
        <result column="REMOVAL_TIME_" property="removalTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID_, TASK_DEF_KEY_, PROC_DEF_KEY_, PROC_DEF_ID_, ROOT_PROC_INST_ID_, PROC_INST_ID_, 
        EXECUTION_ID_, CASE_DEF_KEY_, CASE_DEF_ID_, CASE_INST_ID_, CASE_EXECUTION_ID_, ACT_INST_ID_, 
        NAME_, PARENT_TASK_ID_, DESCRIPTION_, OWNER_, ASSIGNEE_, START_TIME_, END_TIME_, 
        DURATION_, DELETE_REASON_, PRIORITY_, DUE_DATE_, FOLLOW_UP_DATE_, TENANT_ID_, REMOVAL_TIME_
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from ACT_HI_TASKINST
        where ID_ = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from ACT_HI_TASKINST
        where ID_ = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.ActHiTaskinstDO" >
        insert into ACT_HI_TASKINST
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID_,
            </if>
            <if test="taskDefKey != null" >
                TASK_DEF_KEY_,
            </if>
            <if test="procDefKey != null" >
                PROC_DEF_KEY_,
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_,
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_,
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_,
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_,
            </if>
            <if test="caseDefKey != null" >
                CASE_DEF_KEY_,
            </if>
            <if test="caseDefId != null" >
                CASE_DEF_ID_,
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_,
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_,
            </if>
            <if test="actInstId != null" >
                ACT_INST_ID_,
            </if>
            <if test="name != null" >
                NAME_,
            </if>
            <if test="parentTaskId != null" >
                PARENT_TASK_ID_,
            </if>
            <if test="description != null" >
                DESCRIPTION_,
            </if>
            <if test="owner != null" >
                OWNER_,
            </if>
            <if test="assignee != null" >
                ASSIGNEE_,
            </if>
            <if test="startTime != null" >
                START_TIME_,
            </if>
            <if test="endTime != null" >
                END_TIME_,
            </if>
            <if test="duration != null" >
                DURATION_,
            </if>
            <if test="deleteReason != null" >
                DELETE_REASON_,
            </if>
            <if test="priority != null" >
                PRIORITY_,
            </if>
            <if test="dueDate != null" >
                DUE_DATE_,
            </if>
            <if test="followUpDate != null" >
                FOLLOW_UP_DATE_,
            </if>
            <if test="tenantId != null" >
                TENANT_ID_,
            </if>
            <if test="removalTime != null" >
                REMOVAL_TIME_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="taskDefKey != null" >
                #{taskDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefKey != null" >
                #{procDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="rootProcInstId != null" >
                #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="executionId != null" >
                #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="caseDefKey != null" >
                #{caseDefKey,jdbcType=VARCHAR},
            </if>
            <if test="caseDefId != null" >
                #{caseDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="actInstId != null" >
                #{actInstId,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentTaskId != null" >
                #{parentTaskId,jdbcType=VARCHAR},
            </if>
            <if test="description != null" >
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="owner != null" >
                #{owner,jdbcType=VARCHAR},
            </if>
            <if test="assignee != null" >
                #{assignee,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                #{duration,jdbcType=BIGINT},
            </if>
            <if test="deleteReason != null" >
                #{deleteReason,jdbcType=VARCHAR},
            </if>
            <if test="priority != null" >
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="dueDate != null" >
                #{dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="followUpDate != null" >
                #{followUpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="removalTime != null" >
                #{removalTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.ActHiTaskinstDO" >
        update ACT_HI_TASKINST
        <set >
            <if test="taskDefKey != null" >
                TASK_DEF_KEY_ = #{taskDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefKey != null" >
                PROC_DEF_KEY_ = #{procDefKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="rootProcInstId != null" >
                ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="caseDefKey != null" >
                CASE_DEF_KEY_ = #{caseDefKey,jdbcType=VARCHAR},
            </if>
            <if test="caseDefId != null" >
                CASE_DEF_ID_ = #{caseDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="actInstId != null" >
                ACT_INST_ID_ = #{actInstId,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                NAME_ = #{name,jdbcType=VARCHAR},
            </if>
            <if test="parentTaskId != null" >
                PARENT_TASK_ID_ = #{parentTaskId,jdbcType=VARCHAR},
            </if>
            <if test="description != null" >
                DESCRIPTION_ = #{description,jdbcType=VARCHAR},
            </if>
            <if test="owner != null" >
                OWNER_ = #{owner,jdbcType=VARCHAR},
            </if>
            <if test="assignee != null" >
                ASSIGNEE_ = #{assignee,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null" >
                START_TIME_ = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null" >
                END_TIME_ = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null" >
                DURATION_ = #{duration,jdbcType=BIGINT},
            </if>
            <if test="deleteReason != null" >
                DELETE_REASON_ = #{deleteReason,jdbcType=VARCHAR},
            </if>
            <if test="priority != null" >
                PRIORITY_ = #{priority,jdbcType=INTEGER},
            </if>
            <if test="dueDate != null" >
                DUE_DATE_ = #{dueDate,jdbcType=TIMESTAMP},
            </if>
            <if test="followUpDate != null" >
                FOLLOW_UP_DATE_ = #{followUpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null" >
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="removalTime != null" >
                REMOVAL_TIME_ = #{removalTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.ActHiTaskinstDO" >
        select 
        <include refid="Base_Column_List" />
        from ACT_HI_TASKINST
        <where >
            <if test="id != null" >
                and ID_ = #{id,jdbcType=VARCHAR}
            </if>
            <if test="taskDefKey != null" >
                and TASK_DEF_KEY_ = #{taskDefKey,jdbcType=VARCHAR}
            </if>
            <if test="procDefKey != null" >
                and PROC_DEF_KEY_ = #{procDefKey,jdbcType=VARCHAR}
            </if>
            <if test="procDefId != null" >
                and PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR}
            </if>
            <if test="rootProcInstId != null" >
                and ROOT_PROC_INST_ID_ = #{rootProcInstId,jdbcType=VARCHAR}
            </if>
            <if test="procInstId != null" >
                and PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR}
            </if>
            <if test="executionId != null" >
                and EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR}
            </if>
            <if test="caseDefKey != null" >
                and CASE_DEF_KEY_ = #{caseDefKey,jdbcType=VARCHAR}
            </if>
            <if test="caseDefId != null" >
                and CASE_DEF_ID_ = #{caseDefId,jdbcType=VARCHAR}
            </if>
            <if test="caseInstId != null" >
                and CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR}
            </if>
            <if test="caseExecutionId != null" >
                and CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR}
            </if>
            <if test="actInstId != null" >
                and ACT_INST_ID_ = #{actInstId,jdbcType=VARCHAR}
            </if>
            <if test="name != null" >
                and NAME_ = #{name,jdbcType=VARCHAR}
            </if>
            <if test="parentTaskId != null" >
                and PARENT_TASK_ID_ = #{parentTaskId,jdbcType=VARCHAR}
            </if>
            <if test="description != null" >
                and DESCRIPTION_ = #{description,jdbcType=VARCHAR}
            </if>
            <if test="owner != null" >
                and OWNER_ = #{owner,jdbcType=VARCHAR}
            </if>
            <if test="assignee != null" >
                and ASSIGNEE_ = #{assignee,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null" >
                and START_TIME_ = #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null" >
                and END_TIME_ = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="duration != null" >
                and DURATION_ = #{duration,jdbcType=BIGINT}
            </if>
            <if test="deleteReason != null" >
                and DELETE_REASON_ = #{deleteReason,jdbcType=VARCHAR}
            </if>
            <if test="priority != null" >
                and PRIORITY_ = #{priority,jdbcType=INTEGER}
            </if>
            <if test="dueDate != null" >
                and DUE_DATE_ = #{dueDate,jdbcType=TIMESTAMP}
            </if>
            <if test="followUpDate != null" >
                and FOLLOW_UP_DATE_ = #{followUpDate,jdbcType=TIMESTAMP}
            </if>
            <if test="tenantId != null" >
                and TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="removalTime != null" >
                and REMOVAL_TIME_ = #{removalTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
</mapper>