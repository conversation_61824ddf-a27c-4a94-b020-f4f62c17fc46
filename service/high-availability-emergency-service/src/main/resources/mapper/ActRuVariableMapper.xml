<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IActRuVariableDao" >

    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.ActRuVariableDO" >
        <id column="ID_" property="id" jdbcType="VARCHAR" />
        <result column="REV_" property="rev" jdbcType="INTEGER" />
        <result column="TYPE_" property="type" jdbcType="VARCHAR" />
        <result column="NAME_" property="name" jdbcType="VARCHAR" />
        <result column="EXECUTION_ID_" property="executionId" jdbcType="VARCHAR" />
        <result column="PROC_INST_ID_" property="procInstId" jdbcType="VARCHAR" />
        <result column="PROC_DEF_ID_" property="procDefId" jdbcType="VARCHAR" />
        <result column="CASE_EXECUTION_ID_" property="caseExecutionId" jdbcType="VARCHAR" />
        <result column="CASE_INST_ID_" property="caseInstId" jdbcType="VARCHAR" />
        <result column="TASK_ID_" property="taskId" jdbcType="VARCHAR" />
        <result column="BATCH_ID_" property="batchId" jdbcType="VARCHAR" />
        <result column="BYTEARRAY_ID_" property="bytearrayId" jdbcType="VARCHAR" />
        <result column="DOUBLE_" property="double_" jdbcType="DOUBLE" />
        <result column="LONG_" property="long_" jdbcType="BIGINT" />
        <result column="TEXT_" property="text" jdbcType="VARCHAR" />
        <result column="TEXT2_" property="text2" jdbcType="VARCHAR" />
        <result column="VAR_SCOPE_" property="varScope" jdbcType="VARCHAR" />
        <result column="SEQUENCE_COUNTER_" property="sequenceCounter" jdbcType="BIGINT" />
        <result column="IS_CONCURRENT_LOCAL_" property="isConcurrentLocal" jdbcType="TINYINT" />
        <result column="TENANT_ID_" property="tenantId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID_, REV_, TYPE_, NAME_, EXECUTION_ID_, PROC_INST_ID_, PROC_DEF_ID_, CASE_EXECUTION_ID_, 
        CASE_INST_ID_, TASK_ID_, BATCH_ID_, BYTEARRAY_ID_, DOUBLE_, LONG_, TEXT_, TEXT2_, 
        VAR_SCOPE_, SEQUENCE_COUNTER_, IS_CONCURRENT_LOCAL_, TENANT_ID_
    </sql>

    <select id="get" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from act_ru_variable
        where ID_ = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="delete" parameterType="java.lang.String" >
        delete from act_ru_variable
        where ID_ = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.cmpay.hacp.entity.ActRuVariableDO" >
        insert into act_ru_variable
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID_,
            </if>
            <if test="rev != null" >
                REV_,
            </if>
            <if test="type != null" >
                TYPE_,
            </if>
            <if test="name != null" >
                NAME_,
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_,
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_,
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_,
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_,
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_,
            </if>
            <if test="taskId != null" >
                TASK_ID_,
            </if>
            <if test="batchId != null" >
                BATCH_ID_,
            </if>
            <if test="bytearrayId != null" >
                BYTEARRAY_ID_,
            </if>
            <if test="double_ != null" >
                DOUBLE_,
            </if>
            <if test="long_ != null" >
                LONG_,
            </if>
            <if test="text != null" >
                TEXT_,
            </if>
            <if test="text2 != null" >
                TEXT2_,
            </if>
            <if test="varScope != null" >
                VAR_SCOPE_,
            </if>
            <if test="sequenceCounter != null" >
                SEQUENCE_COUNTER_,
            </if>
            <if test="isConcurrentLocal != null" >
                IS_CONCURRENT_LOCAL_,
            </if>
            <if test="tenantId != null" >
                TENANT_ID_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="rev != null" >
                #{rev,jdbcType=INTEGER},
            </if>
            <if test="type != null" >
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="executionId != null" >
                #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null" >
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null" >
                #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="bytearrayId != null" >
                #{bytearrayId,jdbcType=VARCHAR},
            </if>
            <if test="double_ != null" >
                #{double_,jdbcType=DOUBLE},
            </if>
            <if test="long_ != null" >
                #{long_,jdbcType=BIGINT},
            </if>
            <if test="text != null" >
                #{text,jdbcType=VARCHAR},
            </if>
            <if test="text2 != null" >
                #{text2,jdbcType=VARCHAR},
            </if>
            <if test="varScope != null" >
                #{varScope,jdbcType=VARCHAR},
            </if>
            <if test="sequenceCounter != null" >
                #{sequenceCounter,jdbcType=BIGINT},
            </if>
            <if test="isConcurrentLocal != null" >
                #{isConcurrentLocal,jdbcType=TINYINT},
            </if>
            <if test="tenantId != null" >
                #{tenantId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.cmpay.hacp.entity.ActRuVariableDO" >
        update act_ru_variable
        <set >
            <if test="rev != null" >
                REV_ = #{rev,jdbcType=INTEGER},
            </if>
            <if test="type != null" >
                TYPE_ = #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                NAME_ = #{name,jdbcType=VARCHAR},
            </if>
            <if test="executionId != null" >
                EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null" >
                PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null" >
                PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="caseExecutionId != null" >
                CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR},
            </if>
            <if test="caseInstId != null" >
                CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null" >
                TASK_ID_ = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="batchId != null" >
                BATCH_ID_ = #{batchId,jdbcType=VARCHAR},
            </if>
            <if test="bytearrayId != null" >
                BYTEARRAY_ID_ = #{bytearrayId,jdbcType=VARCHAR},
            </if>
            <if test="double_ != null" >
                DOUBLE_ = #{double_,jdbcType=DOUBLE},
            </if>
            <if test="long_ != null" >
                LONG_ = #{long_,jdbcType=BIGINT},
            </if>
            <if test="text != null" >
                TEXT_ = #{text,jdbcType=VARCHAR},
            </if>
            <if test="text2 != null" >
                TEXT2_ = #{text2,jdbcType=VARCHAR},
            </if>
            <if test="varScope != null" >
                VAR_SCOPE_ = #{varScope,jdbcType=VARCHAR},
            </if>
            <if test="sequenceCounter != null" >
                SEQUENCE_COUNTER_ = #{sequenceCounter,jdbcType=BIGINT},
            </if>
            <if test="isConcurrentLocal != null" >
                IS_CONCURRENT_LOCAL_ = #{isConcurrentLocal,jdbcType=TINYINT},
            </if>
            <if test="tenantId != null" >
                TENANT_ID_ = #{tenantId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_ = #{id,jdbcType=VARCHAR}
    </update>

    <select id="find" resultMap="BaseResultMap" parameterType="com.cmpay.hacp.entity.ActRuVariableDO" >
        select 
        <include refid="Base_Column_List" />
        from act_ru_variable
        <where >
            <if test="id != null" >
                and ID_ = #{id,jdbcType=VARCHAR}
            </if>
            <if test="rev != null" >
                and REV_ = #{rev,jdbcType=INTEGER}
            </if>
            <if test="type != null" >
                and TYPE_ = #{type,jdbcType=VARCHAR}
            </if>
            <if test="name != null" >
                and NAME_ = #{name,jdbcType=VARCHAR}
            </if>
            <if test="executionId != null" >
                and EXECUTION_ID_ = #{executionId,jdbcType=VARCHAR}
            </if>
            <if test="procInstId != null" >
                and PROC_INST_ID_ = #{procInstId,jdbcType=VARCHAR}
            </if>
            <if test="procDefId != null" >
                and PROC_DEF_ID_ = #{procDefId,jdbcType=VARCHAR}
            </if>
            <if test="caseExecutionId != null" >
                and CASE_EXECUTION_ID_ = #{caseExecutionId,jdbcType=VARCHAR}
            </if>
            <if test="caseInstId != null" >
                and CASE_INST_ID_ = #{caseInstId,jdbcType=VARCHAR}
            </if>
            <if test="taskId != null" >
                and TASK_ID_ = #{taskId,jdbcType=VARCHAR}
            </if>
            <if test="batchId != null" >
                and BATCH_ID_ = #{batchId,jdbcType=VARCHAR}
            </if>
            <if test="bytearrayId != null" >
                and BYTEARRAY_ID_ = #{bytearrayId,jdbcType=VARCHAR}
            </if>
            <if test="double_ != null" >
                and DOUBLE_ = #{double_,jdbcType=DOUBLE}
            </if>
            <if test="long_ != null" >
                and LONG_ = #{long_,jdbcType=BIGINT}
            </if>
            <if test="text != null" >
                and TEXT_ = #{text,jdbcType=VARCHAR}
            </if>
            <if test="text2 != null" >
                and TEXT2_ = #{text2,jdbcType=VARCHAR}
            </if>
            <if test="varScope != null" >
                and VAR_SCOPE_ = #{varScope,jdbcType=VARCHAR}
            </if>
            <if test="sequenceCounter != null" >
                and SEQUENCE_COUNTER_ = #{sequenceCounter,jdbcType=BIGINT}
            </if>
            <if test="isConcurrentLocal != null" >
                and IS_CONCURRENT_LOCAL_ = #{isConcurrentLocal,jdbcType=TINYINT}
            </if>
            <if test="tenantId != null" >
                and TENANT_ID_ = #{tenantId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>