package com.cmpay.hacp.service.camunda.Impl;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.camunda.ProcessDefinitionService;
import com.cmpay.hacp.utils.TenantUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.engine.ProcessEngineException;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.impl.util.IoUtil;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.rest.dto.repository.ProcessDefinitionDiagramDto;
import org.camunda.bpm.engine.rest.dto.repository.ProcessDefinitionDto;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

import static com.cmpay.hacp.enums.MsgEnum.CASE_NOT_DEPLOY_BPMN;
import static com.cmpay.hacp.enums.MsgEnum.CASE_NOT_EXIST;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/15 9:56
 */
@RequiredArgsConstructor
@Service
public class ProcessDefinitionServiceImpl implements ProcessDefinitionService {
    private static final Logger logger = LoggerFactory.getLogger(ProcessDefinitionService.class);

    private final RepositoryService repositoryService;


    @Override
    public Deployment deployDefinition(String definitionName, String bpmnXml, String tenantId, String res) {
        try {
            return repositoryService.createDeployment()
                    .name(definitionName)
                    .tenantId(tenantId)
                    //检查当前资源是否已部署
                    .enableDuplicateFiltering(true)
                    .addString(res, bpmnXml)
                    .deploy();
        } catch (Exception e){
            logger.error("文件转Bpmn失败", e);
            BusinessException.throwBusinessException(MsgEnum.PROCESS_DEPLOY_FAILED);
        }
        return null;
    }

    @Override
    public Deployment deployDefinition(String name, MultipartFile file, String tenantId) {
        // 先将文件流转换成 bpm对象
        try (InputStream inputStream = file.getInputStream()){
            return repositoryService.createDeployment()
                    .name(name)
                    .tenantId(tenantId)
                    //检查当前资源是否已部署
                    .enableDuplicateFiltering(true)
                    .addInputStream(file.getOriginalFilename(), inputStream)
                    .deploy();
        } catch (Exception e){
            logger.error("文件转Bpmn失败", e);
            BusinessException.throwBusinessException(MsgEnum.PROCESS_DEPLOY_FAILED);
        }
        return null;
    }

    @Override
    public BpmnModelInstance fileToBpmnInstance(MultipartFile file, String definitionName) {
        try (InputStream inputStream = file.getInputStream()) {
            return Bpmn.readModelFromStream(inputStream);
        } catch (IOException e) {
            logger.error("文件转Bpmn失败", e);
            BusinessException.throwBusinessException(MsgEnum.PROCESS_DEPLOY_FAILED);
        }
        return null;
    }

    @Override
    public BpmnModelInstance getBpmnInstance(String processDefinitionId) {
        return repositoryService.getBpmnModelInstance(processDefinitionId);
    }

    @Override
    public Deployment deployDefinition(String definitionName, BpmnModelInstance bpmn, String tenantId, String res) {
        // 先将文件流转换成 bpm对象
        try {
            return repositoryService.createDeployment()
                    .name(definitionName)
                    .tenantId(tenantId)
                    //检查当前资源是否已部署
                    .enableDuplicateFiltering(true)
                    .addModelInstance(res, bpmn)
                    .deploy();
        } catch (Exception e){
            logger.error("文件转Bpmn失败", e);
            BusinessException.throwBusinessException(MsgEnum.PROCESS_DEPLOY_FAILED);
        }
        return null;
    }

    @Override
    public ProcessDefinitionDiagramDto getDefinitionXmlByDeployId(String deployId) {
        logger.info("项目id：{}",TenantUtils.getWorkspaceId());
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .deploymentId(deployId)
                .tenantIdIn(TenantUtils.getWorkspaceIdNotNull())
                .singleResult();
        if (JudgeUtils.isNull(processDefinition)) {
            BusinessException.throwBusinessException(CASE_NOT_DEPLOY_BPMN);
        }
        return getDefinitionXml(processDefinition.getId());
    }

    @Override
    public ProcessDefinitionDiagramDto getDefinitionXml(String id) {
        InputStream processModelIn = repositoryService.getProcessModel(id);
        if (processModelIn == null) {
            BusinessException.throwBusinessException(CASE_NOT_EXIST);
        }
        byte[] processModel = IoUtil.readInputStream(processModelIn, "processModelBpmn20Xml");
        return ProcessDefinitionDiagramDto.create(id, new String(processModel, StandardCharsets.UTF_8));
    }

    @Override
    public void deleteDefinition(String deployId) {
        repositoryService.deleteDeployment(deployId, false);
    }

    @Override
    public ProcessDefinitionDto getProcessDefinition(String deployId) {
        try {
            ProcessDefinition definition = repositoryService.createProcessDefinitionQuery()
                    .deploymentId(deployId)
                    .tenantIdIn(TenantUtils.getWorkspaceId())
                    .singleResult();
            if (JudgeUtils.isNull(definition)) {
                BusinessException.throwBusinessException(CASE_NOT_DEPLOY_BPMN);
            }
            return ProcessDefinitionDto.fromProcessDefinition(definition);
        } catch (ProcessEngineException var4) {
            BusinessException.throwBusinessException(CASE_NOT_EXIST);
        }
        return null;
    }
}
