/*
 * @ClassName ActHiVarinstDO
 * @Description 
 * @version 1.0
 * @Date 2024-10-09 16:42:45
 */
package com.cmpay.hacp.entity;

import com.cmpay.framework.data.BaseDO;
import com.cmpay.lemon.framework.annotation.DataObject;
import java.time.LocalDateTime;

@DataObject
public class ActHiVarinstDO extends BaseDO {
    /**
     * @Fields id
     */
    private String id;
    /**
     * @Fields procDefKey
     */
    private String procDefKey;
    /**
     * @Fields procDefId
     */
    private String procDefId;
    /**
     * @Fields rootProcInstId
     */
    private String rootProcInstId;
    /**
     * @Fields procInstId
     */
    private String procInstId;
    /**
     * @Fields executionId
     */
    private String executionId;
    /**
     * @Fields actInstId
     */
    private String actInstId;
    /**
     * @Fields caseDefKey
     */
    private String caseDefKey;
    /**
     * @Fields caseDefId
     */
    private String caseDefId;
    /**
     * @Fields caseInstId
     */
    private String caseInstId;
    /**
     * @Fields caseExecutionId
     */
    private String caseExecutionId;
    /**
     * @Fields taskId
     */
    private String taskId;
    /**
     * @Fields name
     */
    private String name;
    /**
     * @Fields varType
     */
    private String varType;
    /**
     * @Fields createTime
     */
    private LocalDateTime createTime;
    /**
     * @Fields rev
     */
    private Integer rev;
    /**
     * @Fields bytearrayId
     */
    private String bytearrayId;
    /**
     * @Fields double
     */
    private Double double_;
    /**
     * @Fields long
     */
    private Long long_;
    /**
     * @Fields text
     */
    private String text;
    /**
     * @Fields text2
     */
    private String text2;
    /**
     * @Fields tenantId
     */
    private String tenantId;
    /**
     * @Fields state
     */
    private String state;
    /**
     * @Fields removalTime
     */
    private LocalDateTime removalTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProcDefKey() {
        return procDefKey;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getRootProcInstId() {
        return rootProcInstId;
    }

    public void setRootProcInstId(String rootProcInstId) {
        this.rootProcInstId = rootProcInstId;
    }

    public String getProcInstId() {
        return procInstId;
    }

    public void setProcInstId(String procInstId) {
        this.procInstId = procInstId;
    }

    public String getExecutionId() {
        return executionId;
    }

    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }

    public String getActInstId() {
        return actInstId;
    }

    public void setActInstId(String actInstId) {
        this.actInstId = actInstId;
    }

    public String getCaseDefKey() {
        return caseDefKey;
    }

    public void setCaseDefKey(String caseDefKey) {
        this.caseDefKey = caseDefKey;
    }

    public String getCaseDefId() {
        return caseDefId;
    }

    public void setCaseDefId(String caseDefId) {
        this.caseDefId = caseDefId;
    }

    public String getCaseInstId() {
        return caseInstId;
    }

    public void setCaseInstId(String caseInstId) {
        this.caseInstId = caseInstId;
    }

    public String getCaseExecutionId() {
        return caseExecutionId;
    }

    public void setCaseExecutionId(String caseExecutionId) {
        this.caseExecutionId = caseExecutionId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVarType() {
        return varType;
    }

    public void setVarType(String varType) {
        this.varType = varType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getRev() {
        return rev;
    }

    public void setRev(Integer rev) {
        this.rev = rev;
    }

    public String getBytearrayId() {
        return bytearrayId;
    }

    public void setBytearrayId(String bytearrayId) {
        this.bytearrayId = bytearrayId;
    }

    public Double getDouble_() {
        return double_;
    }

    public void setDouble_(Double double_) {
        this.double_ = double_;
    }

    public Long getLong_() {
        return long_;
    }

    public void setLong_(Long long_) {
        this.long_ = long_;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getText2() {
        return text2;
    }

    public void setText2(String text2) {
        this.text2 = text2;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public LocalDateTime getRemovalTime() {
        return removalTime;
    }

    public void setRemovalTime(LocalDateTime removalTime) {
        this.removalTime = removalTime;
    }
}