package com.cmpay.hacp.bo.process;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/31 10:03
 * @version 1.0
 */

@Getter
@Setter
@ToString
public class StartProcessBO {
    private Map<String, Object> variables;
    private String deploymentId;
    private String deploymentName;
    //private String businessKey;
    /**
     * 用户编号
     */
    private String initiator;
    private ArrayList<String> tenantIds;
    private String businessKey;
    private String workspaceId;
}
