/*
 * @ClassName HacpEmergencyTaskDO
 * @Description 
 * @version 1.0
 * @Date 2024-05-14 15:08:39
 */
package com.cmpay.hacp.bo;

import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/14 15:16
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class HacpExecuteTaskBO extends HacpEmergencyTaskBO {
    private String currentTaskId;

    private String currentTaskName;

    private String assignee;

    private String currentActId;

    private String status;

    private String executeLog;

    private String activityType;

    private Date startTime;

    private Date endTime;

    private Long durationInMillis;
}