/*
 * @ClassName IHacpEmergencyNodeRecodeDao
 * @Description 
 * @version 1.0
 * @Date 2024-10-30 18:22:06
 */
package com.cmpay.hacp.dao;

import com.cmpay.hacp.entity.HacpEmergencyNodeRecodeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IHacpEmergencyNodeRecodeExtDao extends IHacpEmergencyNodeRecodeDao {
    List<HacpEmergencyNodeRecodeDO> getExecuteLastRecode(@Param("businessKey")String businessKey);

    List<HacpEmergencyNodeRecodeDO> getAllRecode(@Param("businessKey")String businessKey);
    List<HacpEmergencyNodeRecodeDO> getAllRecodeParam(@Param("businessKey")String businessKey);

    String getLastExecuteLog(@Param("activityNodeId")String activityNodeId, @Param("businessKey")String businessKey);
    void updateSkipNode(@Param("businessKey")String businessKey, @Param("activityNodeId")String activityNodeId);

    void updateProcessInstanceId(@Param("oldVal")String oldVal, @Param("newVal") String newVal);

    HacpEmergencyNodeRecodeDO getLastExecuteRecode(@Param("businessKey")String businessKey, @Param("activityNodeId")String activityNodeId);
    HacpEmergencyNodeRecodeDO getLastExecuteRecodeAndLog(@Param("businessKey")String businessKey, @Param("activityNodeId")String activityNodeId);

    List<HacpEmergencyNodeRecodeDO> findResult(@Param("businessKey")String businessKey);

    void updateNodeStatus(@Param("businessKey")String businessKey,@Param("activityNodeId") String activityNodeId,@Param("resultLog") String resultLog,@Param("status") String status);

    void deleteExt(HacpEmergencyNodeRecodeDO recodeBO);

    List<HacpEmergencyNodeRecodeDO> getCompleteRecode(@Param("businessKey")String businessKey, @Param("list")List<String> list);
}