/*
 * @ClassName IEmergencyProcessDao
 * @Description 
 * @version 1.0
 * @Date 2024-12-25 11:21:28
 */
package com.cmpay.hacp.dao;

import com.cmpay.hacp.bo.EmergencyProcessBO;
import com.cmpay.hacp.entity.EmergencyProcessDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface IEmergencyProcessExtDao extends IEmergencyProcessDao {
    void deleteExt(EmergencyProcessBO bo);

    EmergencyProcessDO getDetailInfo(EmergencyProcessDO bo);

    void updateState(EmergencyProcessDO entity);

    void updateAuditUserId(EmergencyProcessDO entity);

    List<EmergencyProcessBO> getPage(EmergencyProcessBO bo);

    void updateExt(EmergencyProcessBO bo);
}