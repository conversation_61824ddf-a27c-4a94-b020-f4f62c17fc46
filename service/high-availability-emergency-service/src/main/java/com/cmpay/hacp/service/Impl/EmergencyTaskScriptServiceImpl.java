package com.cmpay.hacp.service.Impl;

import com.cmpay.hacp.bo.EmergencyTaskScriptBO;
import com.cmpay.hacp.dao.IEmergencyTaskScriptExtDao;
import com.cmpay.hacp.entity.EmergencyTaskScriptDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.EmergencyTaskScriptService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/08/21 11:29
 * @since 1.0.0
 */

@Service
@RequiredArgsConstructor
public class EmergencyTaskScriptServiceImpl implements EmergencyTaskScriptService {

    private final IEmergencyTaskScriptExtDao taskScriptDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void add(EmergencyTaskScriptBO bo) {
        if (JudgeUtils.isBlankAny(bo.getScriptName(), bo.getScriptContent(), bo.getScriptDescribe())) {
            BusinessException.throwBusinessException(MsgEnum.PARAM_IS_NULL_ERROR);
        }
        bo.setCreateTime(LocalDateTime.now());
        bo.setUpdateTime(LocalDateTime.now());
        int i = taskScriptDao.insert(bo);
        if (i != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void update(EmergencyTaskScriptBO bo) {
        bo.setUpdateTime(LocalDateTime.now());
        int i = taskScriptDao.update(bo);
        if (i != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void delete(Integer id) {
        EmergencyTaskScriptBO bo = new EmergencyTaskScriptBO();
        bo.setStatus("1");
        bo.setId(id);
        bo.setUpdateTime(LocalDateTime.now());
        int i = taskScriptDao.update(bo);
        if (i != 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_DELETE_FAILED);
        }
    }

    @Override
    public EmergencyTaskScriptBO getDetailInfo(Integer id) {
        EmergencyTaskScriptBO bo = new EmergencyTaskScriptBO();
        bo.setStatus("0");
        bo.setId(id);
        List<EmergencyTaskScriptDO> taskScriptDO = taskScriptDao.find(bo);
        if (JudgeUtils.isEmpty(taskScriptDO)){
            return null;
        }
        return BeanUtils.copyPropertiesReturnDest(new EmergencyTaskScriptBO(), taskScriptDO.get(0));
    }

    @Override
    public PageInfo<EmergencyTaskScriptBO> getPage(int pageNum, int pageSize, EmergencyTaskScriptBO bo) {
        bo.setStatus("0");
        return PageUtils.pageQueryWithCount(pageNum, pageSize, () -> BeanConvertUtil.convertList(
                taskScriptDao.findExt(bo), EmergencyTaskScriptBO.class));
    }

    @Override
    public List<EmergencyTaskScriptBO> getList(EmergencyTaskScriptBO bo) {
        bo.setStatus("0");
        List<EmergencyTaskScriptDO> taskScriptDO = taskScriptDao.find(bo);
        if (JudgeUtils.isEmpty(taskScriptDO)){
            return new ArrayList<>();
        }
        return  BeanConvertUtil.convertList(taskScriptDO, EmergencyTaskScriptBO.class);
    }
}
