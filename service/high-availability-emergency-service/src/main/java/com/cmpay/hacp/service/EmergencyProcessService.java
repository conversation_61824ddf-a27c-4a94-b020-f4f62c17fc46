package com.cmpay.hacp.service;

import com.cmpay.hacp.bo.EmergencyProcessBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

public interface EmergencyProcessService {

    void add(EmergencyProcessBO bo);

    void update(EmergencyProcessBO bo);
    void updateState(String businessKey,String workspaceId,String state);
    void updateAuditUserId(String businessKey,String workspaceId,String userId);

    void delete(EmergencyProcessBO bo);

    EmergencyProcessBO getDetailInfo(EmergencyProcessBO bo);

    PageInfo<EmergencyProcessBO> getPage(int pageNum, int pageSize, EmergencyProcessBO bo);

    List<EmergencyProcessBO> getList(EmergencyProcessBO bo);

    EmergencyProcessBO getDetailInfoByBusKey(String businessKey,String workspaceId);
}
