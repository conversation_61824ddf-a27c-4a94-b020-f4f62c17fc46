package com.cmpay.hacp.bo.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2024/5/27 10:15
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class InterfaceParamBO extends TaskParam{
    /**
     * 请求地址
     */
    private String requestUrl;
    /**
     * 请求类型 POST GET
     */
    private String type;
    /**
     * 请求参数
     */
    private List<Map<String, String>> urlParam;
    /**
     * 请求体
     */
    private String body;
    /**
     * 请求cookie
     */
    private List<Map<String, String>> cookies;
    /**
     * 请求头
     */
    private List<Map<String, String>> headers;

    private String signClass;
    /**
     * 结果判断表达式
     */
    private String responseCheckExpression;
    
}
