package com.cmpay.hacp.service.Impl;

import com.cmpay.hacp.bo.EmergencyAppBO;
import com.cmpay.hacp.dao.IEmergencyAppExtDao;
import com.cmpay.hacp.entity.EmergencyAppDO;
import com.cmpay.hacp.enums.ByteStatusEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.EmergencyAppService;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/08/23 16:07
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmergencyAppServiceImpl implements EmergencyAppService {

    private final IEmergencyAppExtDao emergencyAppExtDao;

    @Override
    public EmergencyAppBO add(EmergencyAppBO bo) {
        EmergencyAppDO entity = new EmergencyAppDO();
        entity.setHostApp(bo.getHostApp());
        entity.setWorkspaceId(bo.getWorkspaceId());
        List<EmergencyAppDO> emergencyAppDOS = emergencyAppExtDao.find(entity);
        if(JudgeUtils.isNotEmpty(emergencyAppDOS)){
            log.error("app is not null {}", emergencyAppDOS);
            BusinessException.throwBusinessException(MsgEnum.DB_THE_SAME_DATA_EXISTS);
        }
        emergencyAppExtDao.insert(bo);
        return bo;
    }

    @Override
    public void update(EmergencyAppBO bo) {
        emergencyAppExtDao.insert(bo);
    }

    @Override
    public void delete(EmergencyAppBO bo) {
        emergencyAppExtDao.deleteExt(bo);
    }

    @Override
    public EmergencyAppBO getDetailInfo(EmergencyAppBO bo) {
        EmergencyAppDO emergencyApp=emergencyAppExtDao.getDetailInfo(bo);
        if(JudgeUtils.isNull(emergencyApp)){
            BusinessException.throwBusinessException(MsgEnum.DB_SELECT_FAILED);
        }
        return BeanConvertUtil.convert(emergencyApp, EmergencyAppBO.class);
    }

    @Override
    public List<EmergencyAppBO> getList(EmergencyAppBO bo) {
        EmergencyAppDO entity = new EmergencyAppDO();
        entity.setStatus(ByteStatusEnum.ENABLE.getValue());
        entity.setWorkspaceId(bo.getWorkspaceId());
        List<EmergencyAppDO> emergencyAppDOS = emergencyAppExtDao.find(entity);
        if(JudgeUtils.isNotEmpty(emergencyAppDOS)){
            return BeanConvertUtil.convertList(emergencyAppDOS,EmergencyAppBO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public Map<String, Integer> getMap(EmergencyAppBO bo) {
        List<EmergencyAppBO> list = this.getList(bo);
        return list.stream()
                .collect(Collectors.toMap(EmergencyAppBO::getHostApp, EmergencyAppBO::getHostAppId));
    }
}
