<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.ITenantWorkspaceRoleExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.TenantWorkspaceRoleDO">
        <id column="workspace_role_id" property="workspaceRoleId" jdbcType="VARCHAR"/>
        <result column="workspace_role_name" property="workspaceRoleName" jdbcType="VARCHAR"/>
        <result column="workspace_role_type" property="workspaceRoleType" jdbcType="VARCHAR"/>
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseResultBoMap" type="com.cmpay.hacp.bo.TenantWorkspaceRoleBO">
        <id column="workspace_role_id" property="workspaceRoleId" jdbcType="VARCHAR"/>
        <result column="workspace_role_name" property="workspaceRoleName" jdbcType="VARCHAR"/>
        <result column="workspace_role_type" property="workspaceRoleType" jdbcType="VARCHAR"/>
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remarks" property="remarks" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="SimpleBaseResultMap" type="com.cmpay.hacp.bo.TenantWorkspaceRoleBO">
        <id column="workspace_role_id" property="workspaceRoleId" jdbcType="VARCHAR"/>
        <result column="workspace_role_name" property="workspaceRoleName" jdbcType="VARCHAR"/>
        <result column="workspace_role_type" property="workspaceRoleType" jdbcType="VARCHAR"/>
        <result column="workspace_id" property="workspaceId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        workspace_role_id
        , workspace_role_name, workspace_role_type, workspace_id, create_user,
        create_time, update_user, update_time, remarks, status
    </sql>

    <select id="getDetailWorkspaceRoles" resultMap="BaseResultBoMap">
        select
        <include refid="Base_Column_List"/>
        from tenant_workspace_role
        <where>
            <if test="workspaceRoleId != null and workspaceRoleId != ''">
                and workspace_role_id = #{workspaceRoleId,jdbcType=VARCHAR}
            </if>
            <if test="workspaceRoleName != null and workspaceRoleName != ''">
                and workspace_role_name = #{workspaceRoleName,jdbcType=VARCHAR}
            </if>
            <if test="workspaceRoleType != null and workspaceRoleType != ''">
                and workspace_role_type = #{workspaceRoleType,jdbcType=VARCHAR}
            </if>
            <if test="workspaceId != null and workspaceId != ''">
                and workspace_id = #{workspaceId,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user = #{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null and remarks != ''">
                and remarks = #{remarks,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and status = #{status,jdbcType=VARCHAR}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="getAdminWorkspaceRoleIds" resultType="java.lang.String">
        select workspace_role_id
        from tenant_workspace_role
        where workspace_role_type = '1'
          and workspace_id = #{workspaceId,jdbcType=VARCHAR}
        order by update_time desc
    </select>

    <select id="getWorkspaceRoleIds" resultType="java.lang.String">
        select workspace_role_id
        from tenant_workspace_role
        where workspace_id = #{workspaceId,jdbcType=VARCHAR}
        order by update_time desc
    </select>

    <select id="getWorkspaceRolesByUserId" resultMap="SimpleBaseResultMap">
        SELECT workspace_role_id, workspace_role_name, workspace_role_type, workspace_id
        FROM tenant_workspace_role
        WHERE workspace_role_id IN (SELECT workspace_role_id
                                    FROM tenant_workspace_user_role
                                    WHERE workspace_role_id IN (SELECT workspace_role_id
                                                                FROM tenant_workspace_role
                                                                WHERE workspace_id = #{workspaceId,jdbcType=VARCHAR})
                                      AND user_id = #{userId,jdbcType=VARCHAR})
        order by update_time desc
    </select>

    <select id="getMenuIdsBySystemWorkspaceRole" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT t2.menu_id
        FROM sys_role t1
                 LEFT JOIN sys_role_menu t2 ON t1.role_id = t2.role_id
        WHERE t1.role_name = #{roleName,jdbcType=VARCHAR}
        order by t2.menu_id asc
    </select>

    <select id="getRoleIdByRoleName" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT role_id
        FROM sys_role
        WHERE role_name = #{roleName,jdbcType=VARCHAR}
    </select>

    <insert id="insertSystemUserRole">
        insert into sys_user_role (id, user_id, role_id)
        values (CONCAT(CONCAT(#{userId}, '-'), #{roleId}), #{userId}, #{roleId})
    </insert>

    <delete id="deleteSystemUserRole">
        delete
        from sys_user_role
        where user_id = #{userId,jdbcType=VARCHAR}
          and role_id = #{roleId,jdbcType=BIGINT}
    </delete>
</mapper>
