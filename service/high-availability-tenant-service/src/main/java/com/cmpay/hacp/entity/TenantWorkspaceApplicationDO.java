/*
 * @ClassName TenantWorkspaceApplicationDO
 * @Description
 * @version 1.0
 * @Date 2023-08-23 16:35:12
 */
package com.cmpay.hacp.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class TenantWorkspaceApplicationDO extends BaseDO {
    /**
     * @Fields applicationId 应用ID
     */
    private String applicationId;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields application 应用名称
     */
    private String application;
    /**
     * @Fields profile 应用运行profile
     */
    private String profile;
    /**
     * @Fields type 类型(0-业务应用、1-网关应用、2-中间件应用)
     */
    private String type;
    /**
     * @Fields status 状态(0-数据库有，服务列表没有，1-数据库有，服务列表有)
     */
    private String status;
    /**
     * @Fields remark 备注
     */
    private String remark;
    /**
     * @Fields createTs 创建时间
     */
    private LocalDateTime createTs;
    /**
     * @Fields createBy 创建人
     */
    private String createBy;
    /**
     * @Fields updateTs 更新时间
     */
    private LocalDateTime updateTs;
    /**
     * @Fields updateBy 更新人
     */
    private String updateBy;
    /**
     * @Fields delete 删除标记('0'-未删除,'1'-'已删除')
     */
    private String delete;

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateTs() {
        return createTs;
    }

    public void setCreateTs(LocalDateTime createTs) {
        this.createTs = createTs;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getUpdateTs() {
        return updateTs;
    }

    public void setUpdateTs(LocalDateTime updateTs) {
        this.updateTs = updateTs;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getDelete() {
        return delete;
    }

    public void setDelete(String delete) {
        this.delete = delete;
    }
}