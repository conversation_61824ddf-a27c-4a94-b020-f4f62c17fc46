# 巡检结果规则匹配实现文档

## 概述

本文档描述了巡检结果内容匹配巡检规则的实现方案。该实现确保只有匹配规则成功的巡检才算通过。

## 核心组件

### 1. ScriptResultParserService
负责解析巡检脚本的结构化输出。

**功能：**
- 支持JSON格式和键值对格式的脚本输出解析
- 根据PluginScriptResultDO字段定义提取相应的字段值
- 支持数值型、字符串型、布尔型字段类型转换

**示例输出格式：**

JSON格式：
```json
{
  "cpu": {
    "usage": 85.5
  },
  "memory": {
    "usage": 1024
  },
  "service": {
    "status": true,
    "name": "nginx"
  }
}
```

键值对格式：
```
cpu.usage=85.5
memory.usage=1024
service.status=true
service.name=nginx
```

### 2. RuleMatchingService
负责匹配解析后的字段值与规则条件。

**功能：**
- 根据RulePluginResultDO中定义的规则条件进行匹配
- 支持大于、小于、等于、大于等于、小于等于等判断条件
- 返回详细的匹配结果和建议

### 3. 增强的InspectionResult
包含了脚本执行结果和规则匹配结果的完整信息。

**新增字段：**
- `ruleMatchingResult`: 规则匹配详细结果
- `scriptExecutionSuccess`: 脚本执行是否成功（技术层面）
- `ruleMatchingSuccess`: 规则匹配是否成功（业务层面）

## 工作流程

1. **脚本执行**：AbstractPluginExecutor执行远程脚本
2. **结果解析**：ScriptResultParserService解析脚本输出
3. **规则匹配**：RuleMatchingService匹配解析结果与规则条件
4. **结果构建**：构建包含完整信息的InspectionResult

## 配置示例

### 插件脚本输出字段定义 (PluginScriptOutputFiledDO)
```sql
INSERT INTO inspection_plugin_script_output_filed (plugin_id, field_name, field_type, field_unit, description) VALUES
('PLUGIN-001', 'cpu.usage', 1, '%', 'CPU使用率'),
('PLUGIN-001', 'memory.usage', 1, 'MB', '内存使用量'),
('PLUGIN-001', 'service.status', 3, '', '服务状态');
```

### 规则条件定义 (RuleConditionDO)
```sql
INSERT INTO rule_condition (rule_id, plugin_id, plugin_result_id, comparison_operator, comparison_value, suggestion) VALUES
('RULE-001', 'PLUGIN-001', 1, 1, 80.0, '请优化CPU使用率，建议值应小于80%');
```

## 判断条件枚举

| 代码 | 描述 | 示例 |
|------|------|------|
| 1 | 大于 | cpu.usage > 80 |
| 2 | 小于 | cpu.usage < 50 |
| 3 | 大于等于 | cpu.usage >= 80 |
| 4 | 小于等于 | cpu.usage <= 50 |
| 5 | 等于 | cpu.usage = 100 |

## 字段类型枚举

| 代码 | 描述 | 转换示例 |
|------|------|----------|
| 1 | 数值型 | "85.5%" → BigDecimal(85.5) |
| 2 | 字符串型 | "nginx" → "nginx" |
| 3 | 布尔型 | "true" → Boolean(true) |

## 使用示例

### 1. 脚本输出示例
```bash
#!/bin/bash
# CPU使用率检查脚本
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
memory_usage=$(free -m | awk 'NR==2{printf "%.1f", $3}')
service_status=$(systemctl is-active nginx)

# JSON格式输出
echo "{"
echo "  \"cpu\": {"
echo "    \"usage\": $cpu_usage"
echo "  },"
echo "  \"memory\": {"
echo "    \"usage\": $memory_usage"
echo "  },"
echo "  \"service\": {"
echo "    \"status\": $([ "$service_status" = "active" ] && echo "true" || echo "false")"
echo "  }"
echo "}"
```

### 2. 规则匹配结果示例
```json
{
  "success": false,
  "scriptExecutionSuccess": true,
  "ruleMatchingSuccess": false,
  "message": "规则匹配失败: 巡检规则失败：字段 cpu.usage 实际值 85.5，期望 大于 80，结果：失败",
  "ruleMatchingResult": {
    "success": false,
    "ruleId": "RULE-001",
    "pluginId": "PLUGIN-001",
    "fieldName": "cpu.usage",
    "actualValue": 85.5,
    "expectedValue": 80,
    "condition": "大于",
    "message": "巡检规则失败：字段 cpu.usage 实际值 85.5，期望 大于 80，结果：失败",
    "suggestion": "请优化CPU使用率，建议值应小于80%"
  }
}
```

## 测试

项目包含了完整的单元测试：
- `ScriptResultParserServiceImplTest`: 测试脚本结果解析功能
- `RuleMatchingServiceImplTest`: 测试规则匹配功能

运行测试：
```bash
./gradlew test --tests "*RuleMatchingServiceImplTest*"
./gradlew test --tests "*ScriptResultParserServiceImplTest*"
```

## 扩展性

该实现具有良好的扩展性：
1. **新增字段类型**：在ScriptResultFieldType枚举中添加新类型
2. **新增判断条件**：在RuleComparisonOperator枚举中添加新条件
3. **新增解析格式**：在ScriptResultParserService中添加新的解析逻辑
4. **自定义规则逻辑**：继承RuleMatchingService实现自定义匹配逻辑

## 注意事项

1. **线程安全**：所有服务都是无状态的，支持并发调用
2. **错误处理**：完善的异常处理和错误信息记录
3. **性能考虑**：解析和匹配过程都进行了优化，支持大量并发请求
4. **数据一致性**：确保规则条件和字段定义的一致性
