package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.ScriptResultParserService;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultFieldType;
import com.cmpay.hacp.inspection.domain.model.rule.RuleMatchingResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptOutputFieldDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptOutputFiledRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleConditionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 规则匹配服务测试
 */
@ExtendWith(MockitoExtension.class)
class RuleMatchingServiceImplTest {

    @Mock
    private ScriptResultParserService scriptResultParserService;

    @Mock
    private RuleConditionRepository ruleConditionRepository;

    @Mock
    private PluginScriptOutputFiledRepository pluginScriptOutputFiledRepository;

    @InjectMocks
    private RuleMatchingServiceImpl ruleMatchingService;

    private RuleConditionDO ruleCondition;
    private PluginScriptOutputFieldDO fieldDefinition;

    @BeforeEach
    void setUp() {
        // 设置规则条件：CPU使用率大于80%
        ruleCondition = new RuleConditionDO();
        ruleCondition.setRuleId("RULE-000001");
        ruleCondition.setPluginId("PLUGIN-000001");
        ruleCondition.setOutputFiledId(1L);
        ruleCondition.setComparisonOperator(RuleComparisonOperator.GREATER_THAN);
        ruleCondition.setComparisonValue(new BigDecimal("80"));
        ruleCondition.setSuggestion("请优化CPU使用率");

        // 设置字段定义
        fieldDefinition = new PluginScriptOutputFieldDO();
        fieldDefinition.setId(1L);
        fieldDefinition.setPluginId("PLUGIN-000001");
        fieldDefinition.setFieldName("cpu.usage");
        fieldDefinition.setFieldType(ScriptResultFieldType.NUMERIC);
        fieldDefinition.setFieldUnit("%");
        fieldDefinition.setDescription("CPU使用率");
    }

    @Test
    void testEvaluateFieldCondition_GreaterThan_Success() {
        // 测试大于条件 - 成功场景
        Object fieldValue = new BigDecimal("85");
        
        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);
        
        assertTrue(result, "85 > 80 应该返回true");
    }

    @Test
    void testEvaluateFieldCondition_GreaterThan_Failure() {
        // 测试大于条件 - 失败场景
        Object fieldValue = new BigDecimal("75");
        
        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);
        
        assertFalse(result, "75 > 80 应该返回false");
    }

    @Test
    void testEvaluateFieldCondition_LessThan() {
        // 测试小于条件
        ruleCondition.setComparisonOperator(RuleComparisonOperator.LESS_THAN);
        ruleCondition.setComparisonValue(new BigDecimal("50"));
        
        Object fieldValue = new BigDecimal("30");
        
        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);
        
        assertTrue(result, "30 < 50 应该返回true");
    }

    @Test
    void testEvaluateFieldCondition_Equal() {
        // 测试等于条件
        ruleCondition.setComparisonOperator(RuleComparisonOperator.EQUAL);
        ruleCondition.setComparisonValue(new BigDecimal("100"));
        
        Object fieldValue = new BigDecimal("100");
        
        boolean result = ruleMatchingService.evaluateFieldCondition(fieldValue, ruleCondition);
        
        assertTrue(result, "100 == 100 应该返回true");
    }

    @Test
    void testMatchParsedValues_Success() {
        // 测试解析值匹配 - 成功场景
        Map<String, Object> parsedValues = new HashMap<>();
        parsedValues.put("cpu.usage", new BigDecimal("85"));

        RuleMatchingResult result = ruleMatchingService.matchParsedValues(parsedValues, ruleCondition, fieldDefinition);

        assertTrue(result.isSuccess());
        assertEquals("RULE-000001", result.getRuleId());
        assertEquals("PLUGIN-000001", result.getPluginId());
        assertEquals("cpu.usage", result.getFieldName());
        assertEquals(new BigDecimal("85"), result.getActualValue());
        assertEquals(new BigDecimal("80"), result.getExpectedValue());
        assertNotNull(result.getMessage());
        assertEquals("请优化CPU使用率", result.getSuggestion());
    }

    @Test
    void testMatchParsedValues_Failure() {
        // 测试解析值匹配 - 失败场景
        Map<String, Object> parsedValues = new HashMap<>();
        parsedValues.put("cpu.usage", new BigDecimal("75"));

        RuleMatchingResult result = ruleMatchingService.matchParsedValues(parsedValues, ruleCondition, fieldDefinition);

        assertFalse(result.isSuccess());
        assertEquals("RULE-000001", result.getRuleId());
        assertEquals("PLUGIN-000001", result.getPluginId());
        assertEquals("cpu.usage", result.getFieldName());
        assertEquals(new BigDecimal("75"), result.getActualValue());
        assertEquals(new BigDecimal("80"), result.getExpectedValue());
    }

    @Test
    void testMatchParsedValues_FieldNotFound() {
        // 测试字段未找到场景
        Map<String, Object> parsedValues = new HashMap<>();
        // 不包含 cpu.usage 字段

        RuleMatchingResult result = ruleMatchingService.matchParsedValues(parsedValues, ruleCondition, fieldDefinition);

        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("Field value not found"));
    }

    @Test
    void testMatchRule_Success() {
        // 测试完整规则匹配流程 - 成功场景
        String scriptOutput = "{\"cpu\":{\"usage\":85.5}}";
        
        // Mock repository calls
        when(ruleConditionRepository.getOne(any())).thenReturn(ruleCondition);
        when(pluginScriptOutputFiledRepository.getById(1L)).thenReturn(fieldDefinition);
        when(pluginScriptOutputFiledRepository.list(any(Wrapper.class))).thenReturn(Collections.singletonList(fieldDefinition));
        
        // Mock parser service
        Map<String, Object> parsedValues = new HashMap<>();
        parsedValues.put("cpu.usage", new BigDecimal("85.5"));
        when(scriptResultParserService.parseScriptOutput(anyString(), any())).thenReturn(parsedValues);

        RuleMatchingResult result = ruleMatchingService.matchRule("RULE-000001", "PLUGIN-000001", scriptOutput);

        assertTrue(result.isSuccess());
        assertEquals("RULE-000001", result.getRuleId());
        assertEquals("PLUGIN-000001", result.getPluginId());
    }

    @Test
    void testMatchRule_RuleNotFound() {
        // 测试规则未找到场景
        when(ruleConditionRepository.getOne(any())).thenReturn(null);

        RuleMatchingResult result = ruleMatchingService.matchRule("RULE-000001", "PLUGIN-000001", "{}");

        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("Rule condition not found"));
    }
}
