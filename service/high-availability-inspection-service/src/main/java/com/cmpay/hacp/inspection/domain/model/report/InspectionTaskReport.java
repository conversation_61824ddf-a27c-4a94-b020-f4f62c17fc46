package com.cmpay.hacp.inspection.domain.model.report;

import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检任务执行报告
 */
@Data
public class InspectionTaskReport {
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 执行ID
     */
    private Long executionId;
    
    /**
     * 触发方式 0:定时触发 1:手动触发
     */
    private TriggerMode triggerMode;
    
    /**
     * 执行状态 0:待执行 1:执行中 2:已完成 3:失败 4:已停止
     */
    private ExecutionStatus executionStatus;
    
    /**
     * 执行结果 pass:通过 fail:失败
     */
    private ExecutionResult executionResult;
    
    /**
     * 计划执行时间
     */
    private LocalDateTime scheduledTime;
    
    /**
     * 实际执行时间
     */
    private LocalDateTime executionTime;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行耗时(秒)
     */
    private Integer duration;
    
    /**
     * 规则总数
     */
    private Integer ruleTotal;
    
    /**
     * 成功规则数
     */
    private Integer ruleSuccessCount;
    
    /**
     * 失败规则数
     */
    private Integer ruleFailCount;
    
    /**
     * 资源覆盖率(%)
     */
    private Integer coverageRate;
    
    /**
     * 规则通过率(%)
     */
    private Integer successRate;
    
    /**
     * 结果概要
     */
    private String resultSummary;
    
    /**
     * 规则执行详情列表
     */
    private List<RuleExecutionDetail> ruleExecutionDetails;
    
    /**
     * 规则执行详情
     */
    @Data
    public static class RuleExecutionDetail {
        /**
         * 规则ID
         */
        private String ruleId;
        
        /**
         * 规则名称
         */
        private String ruleName;
        
        /**
         * 插件ID
         */
        private String pluginId;
        
        /**
         * 插件名称
         */
        private String pluginName;
        
        /**
         * 执行状态 0:失败 1:成功
         */
        private String executionStatus;
        
        /**
         * 执行时间
         */
        private LocalDateTime executionTime;
        
        /**
         * 执行结果详情
         */
        private String executionResult;
        
        /**
         * 执行耗时(毫秒)
         */
        private Long executionDuration;
        
        /**
         * 资源ID
         */
        private String resourceId;
        
        /**
         * 资源名称
         */
        private String resourceName;
    }
}
