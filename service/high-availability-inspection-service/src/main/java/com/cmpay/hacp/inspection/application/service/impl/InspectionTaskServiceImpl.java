package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.assembler.InspectionTaskMapper;
import com.cmpay.hacp.inspection.application.assembler.ScheduleConfigMapper;
import com.cmpay.hacp.inspection.application.service.InspectionTaskExecutionService;
import com.cmpay.hacp.inspection.application.service.InspectionTaskService;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.domain.model.task.InspectionTask;
import com.cmpay.hacp.inspection.domain.model.task.ScheduleConfig;
import com.cmpay.hacp.inspection.domain.model.task.TaskRuleExecution;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.*;
import com.cmpay.hacp.inspection.infrastructure.repository.*;
import com.cmpay.hacp.inspection.infrastructure.scheduler.SchedulerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskServiceImpl implements InspectionTaskService {

    private final InspectionTaskMapper inspectionTaskMapper;
    private final RuleRepository ruleRepository;
    private final TaskRepository taskRepository;
    private final TaskRuleRepository taskRuleRepository;
    private final TaskRuleResourceRepository taskRuleResourceRepository;
    private final ScheduleConfigMapper scheduleConfigMapper;
    private final ScheduleConfigRepository scheduleConfigRepository;
    private final SchedulerService schedulerService;
    private final InspectionTaskExecutionService taskExecutionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTask(InspectionTask inspectionTask) {

        InspectionTaskDO inspectionTaskDO = inspectionTaskMapper.toInspectionTaskDO(inspectionTask);
        inspectionTaskDO.setTaskId("TEMP_TASK" + UUID.randomUUID());
        taskRepository.save(inspectionTaskDO);
        // 业务标识符
        String taskId = String.format("TASK-%06d", inspectionTaskDO.getId());
        inspectionTaskDO.setTaskId(taskId);
        // 只更新业务标识符字段，减少更新范围
        taskRepository.update(Wrappers.lambdaUpdate(InspectionTaskDO.class)
                .eq(InspectionTaskDO::getId, inspectionTaskDO.getId())
                .set(InspectionTaskDO::getTaskId, inspectionTaskDO.getTaskId()));

        inspectionTask.setTaskId(taskId);

        if (CollectionUtils.isNotEmpty(inspectionTask.getTaskRuleExecutions())) {
            List<TaskRuleDO> mappings = inspectionTask.getTaskRuleExecutions().stream()
                    .map(ruleExecution -> {
                        TaskRuleDO mapping = new TaskRuleDO();
                        mapping.setTaskId(taskId);
                        mapping.setRuleId(ruleExecution.getRuleId());
                        return mapping;
                    })
                    .collect(Collectors.toList());

            taskRuleRepository.saveBatch(mappings, 10);

            List<TaskRuleResourceDO> taskRuleResourceDOList = inspectionTask.getTaskRuleExecutions().stream()
                    .flatMap(ruleExecution ->
                            ruleExecution.getTargetEnvironmentId().stream()
                                    .map(targetId -> {
                                        TaskRuleResourceDO resource = new TaskRuleResourceDO();
                                        resource.setTaskId(taskId);
                                        resource.setRuleId(ruleExecution.getRuleId());
                                        resource.setResourceId(targetId);
                                        return resource;
                                    })
                    )
                    .collect(Collectors.toList());
            taskRuleResourceRepository.saveBatch(taskRuleResourceDOList, 20);
        }

        // 保存调度配置
        TaskScheduleConfigDO taskScheduleConfigDO = scheduleConfigMapper.toScheduleConfigDO(inspectionTask.getScheduleConfig());
        taskScheduleConfigDO.setTaskId(taskId);
        scheduleConfigRepository.save(taskScheduleConfigDO);

        if (inspectionTask.getScheduleConfig().isEnabled()) {
            // 调度任务
            try {
                schedulerService.scheduleInspectionTask(inspectionTaskDO.getId(), inspectionTask.getScheduleConfig());
            } catch (SchedulerException e) {
                throw new RuntimeException(e);
            }
        }


        // TODO 通知

        return inspectionTask.getTaskId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTask(InspectionTask inspectionTask) {
        log.info("Updating task with ID: {}", inspectionTask.getTaskId());

        InspectionTaskDO inspectionTaskDO = inspectionTaskMapper.toInspectionTaskDO(inspectionTask);
        inspectionTaskDO.setTaskId(inspectionTask.getTaskId());
        boolean updateTask = taskRepository.update(Wrappers.lambdaUpdate(InspectionTaskDO.class)
                .eq(InspectionTaskDO::getTaskId, inspectionTaskDO.getTaskId())
                .set(InspectionTaskDO::getName, inspectionTaskDO.getName())
                .set(InspectionTaskDO::getDescription, inspectionTaskDO.getDescription())
                .set(InspectionTaskDO::getStatus, inspectionTaskDO.getStatus())
            );

        if (!updateTask) {
            log.error("Failed to update task with ID: {}", inspectionTask.getTaskId());
            return false;
        }

        // 更新规则执行信息
        if (CollectionUtils.isNotEmpty(inspectionTask.getTaskRuleExecutions())) {
            // 先删除原有规则关联
            taskRuleRepository.remove(Wrappers.lambdaQuery(TaskRuleDO.class)
                    .eq(TaskRuleDO::getTaskId, inspectionTask.getTaskId()));

            // 批量插入新规则关联
            List<TaskRuleDO> taskRuleDOs = inspectionTask.getTaskRuleExecutions().stream()
                    .map(ruleExecution -> {
                        TaskRuleDO mapping = new TaskRuleDO();
                        mapping.setTaskId(inspectionTask.getTaskId());
                        mapping.setRuleId(ruleExecution.getRuleId());
                        return mapping;
                    })
                    .collect(Collectors.toList());
            taskRuleRepository.saveBatch(taskRuleDOs, 10);

            // 先删除原有资源关联
            taskRuleResourceRepository.remove(Wrappers.lambdaQuery(TaskRuleResourceDO.class)
                    .eq(TaskRuleResourceDO::getTaskId, inspectionTask.getTaskId()));

            // 批量插入新资源关联
            List<TaskRuleResourceDO> ruleResourceDOs = inspectionTask.getTaskRuleExecutions().stream()
                    .flatMap(ruleExecution ->
                            ruleExecution.getTargetEnvironmentId().stream()
                                    .map(targetId -> {
                                        TaskRuleResourceDO resource = new TaskRuleResourceDO();
                                        resource.setTaskId(inspectionTask.getTaskId());
                                        resource.setRuleId(ruleExecution.getRuleId());
                                        resource.setResourceId(targetId);
                                        return resource;
                                    })
                    )
                    .collect(Collectors.toList());
            taskRuleResourceRepository.saveBatch(ruleResourceDOs, 20);
        }

        // 保存调度配置
        TaskScheduleConfigDO taskScheduleConfigDO = scheduleConfigMapper.toScheduleConfigDO(inspectionTask.getScheduleConfig());
        taskScheduleConfigDO.setTaskId(inspectionTask.getTaskId());
        scheduleConfigRepository.update(Wrappers.lambdaUpdate(TaskScheduleConfigDO.class)
                .eq(TaskScheduleConfigDO::getTaskId, inspectionTask.getTaskId())
                .set(TaskScheduleConfigDO::getScheduleType, taskScheduleConfigDO.getScheduleType())
                .set(TaskScheduleConfigDO::isEnabled, taskScheduleConfigDO.isEnabled())
                .set(TaskScheduleConfigDO::getCronExpression, taskScheduleConfigDO.getCronExpression())
                .set(TaskScheduleConfigDO::getIntervalValue, taskScheduleConfigDO.getIntervalValue())
                .set(TaskScheduleConfigDO::getIntervalUnit, taskScheduleConfigDO.getIntervalUnit())
                .set(TaskScheduleConfigDO::getExecutionDate, taskScheduleConfigDO.getExecutionDate())
                .set(TaskScheduleConfigDO::getExecutionTime, taskScheduleConfigDO.getExecutionTime())
        );


        if (inspectionTask.getScheduleConfig().isEnabled()) {
            // 调度任务
            try {
                schedulerService.scheduleInspectionTask(inspectionTaskDO.getId(), inspectionTask.getScheduleConfig());
            } catch (SchedulerException e) {
                throw new RuntimeException(e);
            }
        }


        // TODO 修改通知

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        taskRepository.remove(Wrappers.lambdaUpdate(InspectionTaskDO.class)
            .eq(InspectionTaskDO::getTaskId, taskId));

        taskRuleRepository.remove(Wrappers.lambdaUpdate(TaskRuleDO.class)
                .eq(TaskRuleDO::getTaskId, taskId));

        taskRuleResourceRepository.remove(Wrappers.lambdaUpdate(TaskRuleResourceDO.class)
        .eq(TaskRuleResourceDO::getTaskId, taskId));

        scheduleConfigRepository.remove(Wrappers.lambdaUpdate(TaskScheduleConfigDO.class)
                .eq(TaskScheduleConfigDO::getTaskId, taskId));

    }

    @Override
    public InspectionTask getTaskDetail(String taskId) {
        InspectionTaskDO taskDO = taskRepository.getOne(Wrappers.lambdaQuery(InspectionTaskDO.class)
                .eq(InspectionTaskDO::getTaskId, taskId));

        List<TaskRuleDO> taskRuleDOS = taskRuleRepository.list(Wrappers.lambdaQuery(TaskRuleDO.class)
                .eq(TaskRuleDO::getTaskId, taskId));

        List<TaskRuleResourceDO> taskRuleResourceDOS = taskRuleResourceRepository.list(Wrappers.lambdaQuery(TaskRuleResourceDO.class)
                .eq(TaskRuleResourceDO::getTaskId, taskId));

        TaskScheduleConfigDO taskScheduleConfigDO = scheduleConfigRepository.getOne(Wrappers.lambdaQuery(TaskScheduleConfigDO.class)
                .eq(TaskScheduleConfigDO::getTaskId, taskId));


        InspectionTask inspectionTask = inspectionTaskMapper.toInspectionTask(taskDO);
        inspectionTask.setScheduleConfig(scheduleConfigMapper.toScheduleConfig(taskScheduleConfigDO));
        // 把taskRuleResourceDOS按照ruleId挂到taskRuleDOS上面去
        if (CollectionUtils.isNotEmpty(taskRuleDOS)) {

            // 获取所有规则ID
            List<String> ruleIds = taskRuleDOS.stream()
                    .map(TaskRuleDO::getRuleId)
                    .collect(Collectors.toList());
            // 查询规则详情
            List<InspectionRuleDO> ruleDOs = ruleRepository.list(Wrappers.lambdaQuery(InspectionRuleDO.class)
                    .in(InspectionRuleDO::getRuleId, ruleIds));
            // 构建规则ID到规则名称的映射
            Map<String, String> ruleMap = ruleDOs.stream()
                    .collect(Collectors.toMap(
                            InspectionRuleDO::getRuleId,
                            InspectionRuleDO::getName
                    ));

            List<TaskRuleExecution> taskRuleExecutions = inspectionTaskMapper.toTaskRuleExecutions(taskRuleDOS);

            if (CollectionUtils.isNotEmpty(taskRuleResourceDOS)) {
                // 1、按ruleId分组resourceId
                Map<String, List<String>> ruleIdToResourceIdsMap = taskRuleResourceDOS.stream()
                        .collect(Collectors.groupingBy(
                                TaskRuleResourceDO::getRuleId,
                                Collectors.mapping(TaskRuleResourceDO::getResourceId, Collectors.toList())
                        ));
                // 2、将resourceIds和ruleName设置到对应的TaskRuleExecution中
                taskRuleExecutions.forEach(ruleExecution -> {
                    List<String> resourceIds = ruleIdToResourceIdsMap.get(ruleExecution.getRuleId());
                    if (resourceIds != null) {
                        ruleExecution.setTargetEnvironmentId(resourceIds);
                    }
                    // 设置规则名称
                    ruleExecution.setRuleName(ruleMap.get(ruleExecution.getRuleId()));
                });
            }

            inspectionTask.setTaskRuleExecutions(taskRuleExecutions);
        }

        return inspectionTask;
    }

    @Override
    public IPage<InspectionTask> getTaskPage(IPage<?> page, InspectionTask queryCondition) {
        // 参数校验：处理page为null的情况，并设置默认分页参数
        if (page == null) {
            page = new PageDTO<>();
            page.setCurrent(1);
            page.setSize(10); // 默认每页10条
        }

        // 转换查询条件对象
        InspectionTaskDO queryDO = inspectionTaskMapper.toInspectionTaskDO(queryCondition);

        // 构建MyBatis-Plus分页对象
        Page<InspectionTaskDO> mpPage = new Page<>(page.getCurrent(), page.getSize());

        // 构造查询条件
        LambdaQueryWrapper<InspectionTaskDO> queryWrapper = Wrappers.lambdaQuery(InspectionTaskDO.class)
                .eq(queryDO.getId() != null, InspectionTaskDO::getId, queryDO.getId())
                .like(StringUtils.isNotBlank(queryDO.getName()), InspectionTaskDO::getName, queryDO.getName())
                .eq(queryDO.getStatus() != null, InspectionTaskDO::getStatus, queryDO.getStatus())
                .orderByAsc(InspectionTaskDO::getId);

        // 执行分页查询
        IPage<InspectionTaskDO> taskDOPage = taskRepository.page(mpPage, queryWrapper);

        // 转换结果为领域对象
        IPage<InspectionTask> taskIPage = taskDOPage.convert(inspectionTaskMapper::toInspectionTask);

        // 处理关联数据
        if (!taskIPage.getRecords().isEmpty()) {
            enrichInspectionTasks(taskIPage.getRecords());
        }

        return taskIPage;
    }

    private void enrichInspectionTasks(List<InspectionTask> records) {
        List<String> taskIds = records.stream()
                .map(InspectionTask::getTaskId)
                .collect(Collectors.toList());

        // 批量查询相关数据
        List<TaskScheduleConfigDO> scheduleConfigDOS = scheduleConfigRepository.list(Wrappers.lambdaQuery(TaskScheduleConfigDO.class)
                .in(TaskScheduleConfigDO::getTaskId, taskIds));

        // 组装规则、资源信息
        List<TaskRuleDO> taskRuleDOS = taskRuleRepository.list(Wrappers.lambdaQuery(TaskRuleDO.class)
                .in(TaskRuleDO::getTaskId, taskIds));

        List<String> ruleIds = taskRuleDOS.stream()
                .map(TaskRuleDO::getRuleId)
                .collect(Collectors.toList());

        List<InspectionRuleDO> rules = ruleRepository.list(Wrappers.lambdaQuery(InspectionRuleDO.class)
                .in(InspectionRuleDO::getRuleId, ruleIds));

        List<TaskRuleResourceDO> resourceDOS = taskRuleResourceRepository.list(Wrappers.lambdaQuery(TaskRuleResourceDO.class)
                .in(TaskRuleResourceDO::getRuleId, ruleIds));

        // 构建规则ID到规则名称的映射
        Map<String, String> ruleIdToNameMap = rules.stream()
                .collect(Collectors.toMap(
                        InspectionRuleDO::getRuleId,
                        InspectionRuleDO::getName
                ));

        // 构建规则ID到资源ID列表的映射
        Map<String, List<String>> ruleIdToResourcesMap = resourceDOS.stream()
                .collect(Collectors.groupingBy(
                        TaskRuleResourceDO::getRuleId,
                        Collectors.mapping(TaskRuleResourceDO::getResourceId, Collectors.toList())
                ));

        // 构建任务ID到规则执行列表的映射
        Map<String, List<TaskRuleExecution>> taskRuleExecutionMap = taskRuleDOS.stream()
                .collect(Collectors.groupingBy(
                        TaskRuleDO::getTaskId,
                        Collectors.mapping(taskRuleDO -> {
                            TaskRuleExecution execution = new TaskRuleExecution();
                            execution.setTaskId(taskRuleDO.getTaskId());
                            execution.setRuleId(taskRuleDO.getRuleId());
                            execution.setRuleName(ruleIdToNameMap.get(taskRuleDO.getRuleId()));
                            execution.setTargetEnvironmentId(ruleIdToResourcesMap.getOrDefault(taskRuleDO.getRuleId(), Collections.emptyList()));
                            return execution;
                        }, Collectors.toList())
                ));


        // 设置调度配置信息
        Map<String, TaskScheduleConfigDO> scheduleConfigMap = scheduleConfigDOS.stream()
                .collect(Collectors.toMap(TaskScheduleConfigDO::getTaskId, Function.identity()));

        records.forEach(task -> {
            // 将规则执行信息设置到对应的任务中
            List<TaskRuleExecution> executions = taskRuleExecutionMap.get(task.getTaskId());
            if (executions != null) {
                task.setTaskRuleExecutions(executions);
            }
            // 将调度配置信息设置到对应的任务中
            TaskScheduleConfigDO config = scheduleConfigMap.get(task.getTaskId());
            if (config != null) {
                task.setScheduleConfig(scheduleConfigMapper.toScheduleConfig(config));
            }
        });
    }

    @Override
    public void executeTaskNow(String taskId) {
        log.info("Manually executing task: {}", taskId);

        // 验证任务是否存在
        InspectionTaskDO taskDO = taskRepository.getOne(
                Wrappers.lambdaQuery(InspectionTaskDO.class)
                        .eq(InspectionTaskDO::getTaskId, taskId));

        if (taskDO == null) {
            log.error("Cannot execute task: Task not found with ID {}", taskId);
            throw new RuntimeException("Task not found: " + taskId);
        }

        // 调用执行服务执行任务
        taskExecutionService.executeTask(taskId, TriggerMode.MANUAL);

        log.info("Task execution initiated: {}", taskId);
    }
}
