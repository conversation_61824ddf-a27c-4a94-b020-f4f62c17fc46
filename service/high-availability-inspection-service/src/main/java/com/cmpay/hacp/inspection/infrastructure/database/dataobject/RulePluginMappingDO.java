package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则插件关联表实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("rule_plugin_mapping")
public class RulePluginMappingDO extends BaseDO {
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 插件ID
     */
    private String pluginId;

}
