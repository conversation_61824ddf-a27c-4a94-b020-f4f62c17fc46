package com.cmpay.hacp.inspection.application.executor.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.executor.*;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.InspectionPluginDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PluginScriptParameterDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleResourceDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptParameterRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PluginScriptRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.RuleResourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShellPluginExecutor implements PluginExecutor {

    private final RemoteExecutionService remoteExecutionService;

    private final PluginScriptParameterRepository pluginScriptParameterRepository;

    private final PluginScriptRepository pluginScriptRepository;

    private final RuleResourceRepository ruleResourceRepository;

    @Override
    public InspectionResult execute(InspectionPluginDO inspectionPluginDO) {
        log.info("Executing Shell inspection script, Plugin ID: {}, Target: {}", inspectionPluginDO.getPluginId(), null);

        PluginScriptDO pluginScriptDO = pluginScriptRepository.getOne(
                Wrappers.lambdaQuery(PluginScriptDO.class)
                        .eq(PluginScriptDO::getPluginId, inspectionPluginDO.getPluginId()));

        String scriptContent = pluginScriptDO.getScriptContent();

        // 获取param_name到param_value的映射
        Map<String, Object> objectMap = pluginScriptParameterRepository.getMap(
                Wrappers.lambdaQuery(PluginScriptParameterDO.class)
                        .select(PluginScriptParameterDO::getParamName, PluginScriptParameterDO::getParamValue)
                        .eq(PluginScriptParameterDO::getPluginId, inspectionPluginDO.getPluginId()));

        // 转换为Map<String, String>
        Map<String, String> paramMap = objectMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> String.valueOf(entry.getValue())
                ));

        // 替换脚本中的参数
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            scriptContent = scriptContent.replace("${" + entry.getKey() + "}", entry.getValue());
        }

        String ruleId = "";
        RuleResourceDO ruleResourceDO = ruleResourceRepository.getOne(
                Wrappers.lambdaQuery(RuleResourceDO.class)
                        .eq(RuleResourceDO::getRuleId, ruleId));

        // 执行远程Shell脚本
        RemoteExecutionResult result = remoteExecutionService.executeScript(
                // FIXME 资源处理
                SshConnectionConfig.builder()
                        .host(ruleResourceDO.getResourceId())
                        .port(22)
                        .username("root")
                        .password("Cmpay@123")
                        .build(),
                ScriptExecutionRequest.builder()
                        .scriptContent(scriptContent)
                        .scriptType(ScriptExecutionRequest.ScriptType.SHELL)
                        .build()
        );

        boolean isSuccess = result.isSuccess();
        return InspectionResult.builder()
                .success(isSuccess)
                .details(isSuccess ? result.getStdout() : result.getStderr())
                .build();

    }

    @Override
    public String getName() {
        return "Shell Script Inspection";
    }

    @Override
    public boolean supports(PluginType delimiter) {
        return delimiter == PluginType.SHELL_SCRIPT;
    }
}
