package com.cmpay.hacp.inspection.domain.model.plugin;

import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultFieldType;
import lombok.Data;

/**
 * 插件脚本输出字段对象
 */
@Data
public class PluginScriptResult {
    /**
     * 字段名称(如cpu.usage)
     */
    private String fieldName;

    /**
     * 示例值
     */
    private String exampleValue;

    /**
     * 单位(如%)
     */
    private String fieldUnit;

    /**
     * 字段类型(0:数值型, 1:字符串型, 2:布尔型)
     */
    private ScriptResultFieldType fieldType;

    /**
     * 描述
     */
    private String description;
}
