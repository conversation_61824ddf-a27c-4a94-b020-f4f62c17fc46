package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.InspectionTaskMonitoringService;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionStatusDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleExecutionStatusDO;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskExecutionStatusRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.TaskRuleExecutionStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 巡检任务监控服务
 * 负责跟踪和更新任务执行状态
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskMonitoringServiceImpl implements InspectionTaskMonitoringService {
    private final TaskExecutionStatusRepository taskExecutionStatusRepository;
    private final TaskRuleExecutionStatusRepository taskRuleExecutionStatusRepository;
    
    // 内存中的任务执行状态缓存，用于快速访问
    private final Map<String, TaskExecutionStatusDO> activeTaskExecutions = new ConcurrentHashMap<>();
    
    /**
     * 初始化任务执行状态
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param triggerMode 触发方式 0:定时触发 1:手动触发
     * @param ruleTotal 规则总数
     */
    @Override
    public void initializeTaskExecution(String taskId, String taskName, TriggerMode triggerMode, Integer ruleTotal) {
        TaskExecutionStatusDO executionStatus = new TaskExecutionStatusDO();
        executionStatus.setTaskId(taskId);
        executionStatus.setTaskName(taskName);
        executionStatus.setTriggerMode(triggerMode);
        executionStatus.setExecutionStatus(ExecutionStatus.PENDING);
        executionStatus.setScheduledTime(LocalDateTime.now());
        executionStatus.setRuleTotal(ruleTotal);
        executionStatus.setRuleSuccessCount(0);
        executionStatus.setRuleFailCount(0);
        executionStatus.setResultSummary("Task initialized");
        
        taskExecutionStatusRepository.save(executionStatus);
        
        // 缓存活动任务
        activeTaskExecutions.put(taskId, executionStatus);
        
        log.info("Task execution initialized: {}, status ID: {}", taskId, executionStatus.getId());
    }
    
    /**
     * 更新任务为执行中状态
     * @param taskId 任务ID
     */
    @Override
    public void markTaskAsRunning(String taskId) {
        TaskExecutionStatusDO executionStatus = getTaskExecutionStatus(taskId);
        if (executionStatus != null) {
            executionStatus.setExecutionStatus(ExecutionStatus.RUNNING); // 执行中
            executionStatus.setStartTime(LocalDateTime.now());
            executionStatus.setExecutionTime(LocalDateTime.now());
            executionStatus.setStatusSummary("Task execution in progress");
            
            taskExecutionStatusRepository.updateById(executionStatus);
            log.info("Task marked as running: {}", taskId);
        }
    }
    
    /**
     * 更新任务为已完成状态
     * @param taskId 任务ID
     * @param success 是否成功
     */
    @Override
    public void markTaskAsCompleted(String taskId, boolean success) {
        TaskExecutionStatusDO executionStatus = getTaskExecutionStatus(taskId);
        if (executionStatus != null) {
            LocalDateTime endTime = LocalDateTime.now();
            executionStatus.setExecutionStatus(success ? ExecutionStatus.COMPLETED : ExecutionStatus.FAILED); // 2:已完成 3:失败
            executionStatus.setExecutionResult(success ? ExecutionResult.PASS : ExecutionResult.FAIL);
            executionStatus.setEndTime(endTime);
            
            // 计算执行时长（秒）
            if (executionStatus.getStartTime() != null) {
                long durationSeconds = ChronoUnit.SECONDS.between(executionStatus.getStartTime(), endTime);
                executionStatus.setDuration((int) durationSeconds);
            }
            
            // 计算成功率
            if (executionStatus.getRuleTotal() > 0) {
                int successRate = (executionStatus.getRuleSuccessCount() * 100) / executionStatus.getRuleTotal();
                executionStatus.setSuccessRate(successRate);
            }

            // TODO 计算资源覆盖率

            
            executionStatus.setResultSummary(success ? 
                    "Task completed successfully" : 
                    "Task completed with failures");
            
            taskExecutionStatusRepository.updateById(executionStatus);
            
            // 从活动任务缓存中移除
            activeTaskExecutions.remove(taskId);
            
            log.info("Task marked as completed: {}, success: {}", taskId, success);
        }
    }
    
    /**
     * 记录规则执行结果
     * @param taskId 任务ID
     * @param ruleId 规则ID
     * @param pluginId 插件ID
     * @param result 执行结果
     */
    @Override
    public void recordRuleExecutionResult(String taskId, String ruleId, String pluginId, InspectionResult result) {
        TaskExecutionStatusDO taskStatus = getTaskExecutionStatus(taskId);
        if (taskStatus == null) {
            log.warn("Cannot record rule execution result: task status not found for taskId: {}", taskId);
            return;
        }
        
        // 创建规则执行状态记录
        TaskRuleExecutionStatusDO ruleStatus = new TaskRuleExecutionStatusDO();
        ruleStatus.setTaskExecutionId(taskStatus.getId());
        ruleStatus.setRuleId(ruleId);
        ruleStatus.setPluginId(pluginId);
        ruleStatus.setExecutionStatus(result.isSuccess() ? "1" : "0"); // 1:成功 0:失败
        ruleStatus.setExecutionTime(LocalDateTime.now());
        ruleStatus.setExecutionResult(result.getDetails());
        ruleStatus.setPluginName(result.getPluginName());
        
        taskRuleExecutionStatusRepository.save(ruleStatus);
        
        // 更新任务执行状态中的规则执行计数
        if (result.isSuccess()) {
            taskStatus.setRuleSuccessCount(taskStatus.getRuleSuccessCount() + 1);
        } else {
            taskStatus.setRuleFailCount(taskStatus.getRuleFailCount() + 1);
        }
        
        taskExecutionStatusRepository.updateById(taskStatus);
        
        log.info("Rule execution result recorded: taskId={}, ruleId={}, success={}", 
                taskId, ruleId, result.isSuccess());
    }
    
    /**
     * 获取任务执行状态
     * @param taskId 任务ID
     * @return 任务执行状态
     */
    @Override
    public TaskExecutionStatusDO getTaskExecutionStatus(String taskId) {
        // 先从缓存中获取
        TaskExecutionStatusDO cachedStatus = activeTaskExecutions.get(taskId);
        if (cachedStatus != null) {
            return cachedStatus;
        }
        
        // 缓存中没有，从数据库查询
        return taskExecutionStatusRepository.getOne(
                Wrappers.lambdaQuery(TaskExecutionStatusDO.class)
                        .eq(TaskExecutionStatusDO::getTaskId, taskId)
                        .orderByDesc(TaskExecutionStatusDO::getId)
                        .last("LIMIT 1"));
    }
}
