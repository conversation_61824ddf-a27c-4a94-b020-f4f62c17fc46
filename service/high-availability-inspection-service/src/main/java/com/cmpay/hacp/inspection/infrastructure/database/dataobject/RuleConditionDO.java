package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cmpay.hacp.inspection.domain.model.enums.RuleCheckPeakEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 规则监控字段表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("rule_condition")
public class RuleConditionDO extends BaseDO {

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 插件ID
     */
    private String pluginId;

    /**
     * 监控字段（插件输出字段ID）
     */
    private Long outputFiledId;

    /**
     * 判断条件(大小等于)
     * {@link RuleComparisonOperator}
     */
    private RuleComparisonOperator comparisonOperator;

    /**
     * 数值
     */
    private BigDecimal comparisonValue;

    /**
     * 持续时间（秒）
     */
    private Integer duration;

    /**
     * 检查间隔（秒）
     */
    private Integer checkInterval;

    /**
     * 仅检查峰值
     * {@link RuleCheckPeakEnum}
     */
    private RuleCheckPeakEnum checkPeak;

    /**
     * 检查特定进程（逗号分隔）
     */
    private Integer specificProcesses;

    /**
     * 规则预览
     */
    private String rulePreview;

    /**
     * 治理建议
     */
    private String suggestion;

}
