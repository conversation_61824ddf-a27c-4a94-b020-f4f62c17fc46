package com.cmpay.hacp.inspection.domain.model.task;

import com.cmpay.hacp.inspection.domain.model.common.AuditInfo;
import com.cmpay.hacp.inspection.domain.model.enums.TaskStatus;
import lombok.Data;

import java.util.List;

/**
 * 巡检任务聚合根
 */
@Data
public class InspectionTask {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务状态
     */
    private TaskStatus status;

    /**
     * 任务关联的规则执行对象列表
     */
    private List<TaskRuleExecution> taskRuleExecutions;

    /**
     * 调度配置
     */
    private ScheduleConfig scheduleConfig;

    // TODO 通知配置

    private AuditInfo auditInfo;
}
