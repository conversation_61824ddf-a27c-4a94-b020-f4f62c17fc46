package com.cmpay.hacp.inspection.application.executor;

import com.cmpay.hacp.inspection.domain.model.plugin.InspectionResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.InspectionPluginDO;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleResourceDO;
import org.springframework.plugin.core.Plugin;

import java.util.List;

/**
 * 巡检插件接口
 */
public interface PluginExecutor extends Plugin<PluginType> {
    /**
     * 执行巡检
     * @param ruleId 规则ID
     * @param pluginId 插件ID
     * @param targetHost 目标资源
     * @return 巡检结果
     */
    InspectionResult execute(String ruleId, String pluginId, TargetHost targetHost);

    /**
     * 获取插件名称
     * @return 插件名称
     */
    String getName();
}
