package com.cmpay.hacp.inspection.application.executor;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@Builder
public class TargetHost {
    private String hostId;
    private String hostName;
    private String host;
    private int port;
    private String username;
    private AuthType authType;
    private String password;
    private String privateKeyPath;
    private String privateKeyContent;
    private Map<String, String> tags;

    public enum AuthType {
        PASSWORD, PRIVATE_KEY
    }

    // 转换为SSH连接配置
    public SshConnectionConfig toSshConnectionConfig() {
        return SshConnectionConfig.builder()
                .host(this.host)
                .port(this.port)
                .username(this.username)
                .password(this.password)
                .privateKeyPath(this.privateKeyPath)
                .privateKeyContent(this.privateKeyContent)
                .build();
    }
}
