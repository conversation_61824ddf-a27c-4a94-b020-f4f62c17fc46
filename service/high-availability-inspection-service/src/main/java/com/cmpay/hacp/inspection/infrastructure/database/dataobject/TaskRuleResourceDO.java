package com.cmpay.hacp.inspection.infrastructure.database.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 规则资源关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_task_rule_resource")
public class TaskRuleResourceDO extends BaseDO{

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 资源ID
     */
    private String resourceId;
}
