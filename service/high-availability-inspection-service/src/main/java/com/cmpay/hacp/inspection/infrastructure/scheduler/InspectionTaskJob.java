package com.cmpay.hacp.inspection.infrastructure.scheduler;

import com.cmpay.hacp.inspection.application.service.InspectionTaskExecutionService;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InspectionTaskJob extends QuartzJobBean {
    private final InspectionTaskExecutionService inspectionTaskExecutionService;

    public InspectionTaskJob(InspectionTaskExecutionService inspectionTaskExecutionService) {
        this.inspectionTaskExecutionService = inspectionTaskExecutionService;
    }


    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        try {
            log.info("Starting inspection task schedule execution, jobKey: {}", context.getJobDetail().getKey());

            // 从JobDataMap获取任务ID
            String taskId = (String) context.getMergedJobDataMap().get("taskId");

            // 调用应用层服务执行实际业务逻辑
            inspectionTaskExecutionService.executeTask(taskId, TriggerMode.SCHEDULED);

            log.info("Inspection task schedule execution completed, jobKey: {}", context.getJobDetail().getKey());
        } catch (Exception e) {
            log.error("Inspection task schedule execution failed", e);
            throw new JobExecutionException(e);
        }
    }
}
