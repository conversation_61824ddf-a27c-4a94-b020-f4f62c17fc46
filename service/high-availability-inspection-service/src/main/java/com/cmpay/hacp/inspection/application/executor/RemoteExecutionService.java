package com.cmpay.hacp.inspection.application.executor;

import lombok.extern.slf4j.Slf4j;
import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.channel.ChannelExec;
import org.apache.sshd.client.channel.ClientChannelEvent;
import org.apache.sshd.client.future.AuthFuture;
import org.apache.sshd.client.future.ConnectFuture;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.common.NamedResource;
import org.apache.sshd.common.util.security.SecurityUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.security.KeyPair;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
@EnableConfigurationProperties(SshProperties.class)
@Slf4j
public class RemoteExecutionService implements InitializingBean, DisposableBean {
    private final SshProperties sshProperties;
    private SshClient sshClient;

    public RemoteExecutionService(SshProperties sshProperties) {
        this.sshProperties = sshProperties;
    }

    @Override
    public void afterPropertiesSet() {
        sshClient = SshClient.setUpDefaultClient();
        sshClient.start();
        log.info("SSH client initialization completed");
    }

    @Override
    public void destroy() {
        if (sshClient != null && !sshClient.isClosed()) {
            sshClient.stop();
            log.info("SSH client has been gracefully shut down");
        }
    }

    /**
     * 执行远程脚本
     */
    public RemoteExecutionResult executeScript(SshConnectionConfig connectionConfig,
                                               ScriptExecutionRequest request) {
        // 检查客户端状态
        if (sshClient == null || sshClient.isClosed()) {
            return RemoteExecutionResult.builder()
                    .success(false)
                    .errorMessage("SSH client is not initialized or has been shut down")
                    .build();
        }

        long startTime = System.currentTimeMillis();

        try (ClientSession session = createSession(connectionConfig)) {
            if (!authenticate(session, connectionConfig)) {
                return RemoteExecutionResult.builder()
                        .success(false)
                        .errorMessage("SSH authentication failed")
                        .executionTime(System.currentTimeMillis() - startTime)
                        .build();
            }

            // 创建临时脚本文件
            String scriptPath = createTempScript(session, request);

            try {
                // 执行脚本
                return executeRemoteScript(session, scriptPath, request);
            } finally {
                // 清理临时文件
                cleanupTempScript(session, scriptPath);
            }

        } catch (Exception e) {
            log.error("Remote script execution failed", e);
            return RemoteExecutionResult.builder()
                    .success(false)
                    .errorMessage("Execution exception:" + e.getMessage())
                    .executionTime(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    /**
     * 执行简单命令
     */
    public RemoteExecutionResult executeCommand(SshConnectionConfig connectionConfig,
                                                String command) {
        return executeCommand(connectionConfig, command, sshProperties.getExecutionTimeout());
    }

    public RemoteExecutionResult executeCommand(SshConnectionConfig connectionConfig,
                                                String command, int timeoutMs) {
        long startTime = System.currentTimeMillis();

        try (ClientSession session = createSession(connectionConfig)) {
            if (!authenticate(session, connectionConfig)) {
                return RemoteExecutionResult.builder()
                        .success(false)
                        .errorMessage("SSH authentication failed")
                        .build();
            }

            try (ChannelExec channelExec = session.createExecChannel(command)) {
                ByteArrayOutputStream stdout = new ByteArrayOutputStream();
                ByteArrayOutputStream stderr = new ByteArrayOutputStream();
                channelExec.setOut(stdout);
                channelExec.setErr(stderr);

                channelExec.open().verify(sshProperties.getConnectionTimeout());
                channelExec.waitFor(EnumSet.of(ClientChannelEvent.CLOSED), timeoutMs);

                Integer exitCode = channelExec.getExitStatus();
                long executionTime = System.currentTimeMillis() - startTime;

                return RemoteExecutionResult.builder()
                        .exitCode(exitCode != null ? exitCode : -1)
                        .stdout(stdout.toString(StandardCharsets.UTF_8.name()))
                        .stderr(stderr.toString(StandardCharsets.UTF_8.name()))
                        .success(exitCode != null && exitCode == 0)
                        .executionTime(executionTime)
                        .build();
            }

        } catch (Exception e) {
            log.error("Remote command execution failed: {}", command, e);
            return RemoteExecutionResult.builder()
                    .success(false)
                    .errorMessage("Execution exception:" + e.getMessage())
                    .executionTime(System.currentTimeMillis() - startTime)
                    .build();
        }
    }

    private ClientSession createSession(SshConnectionConfig config) throws IOException {
        ConnectFuture connectFuture = sshClient.connect(
                config.getUsername(),
                config.getHost(),
                config.getPort()
        );

        return connectFuture.verify(sshProperties.getConnectionTimeout()).getSession();
    }

    private boolean authenticate(ClientSession session, SshConnectionConfig config)
            throws IOException, GeneralSecurityException {

        // 密码认证
        if (StringUtils.hasText(config.getPassword())) {
            session.addPasswordIdentity(config.getPassword());
            AuthFuture authFuture = session.auth();
            return authFuture.verify(sshProperties.getConnectionTimeout()).isSuccess();
        }

        // 公钥认证
        if (StringUtils.hasText(config.getPrivateKeyPath()) ||
                StringUtils.hasText(config.getPrivateKeyContent())) {

            List<KeyPair> keyPairs = loadKeyPairs(config);
            for (KeyPair keyPair : keyPairs) {
                session.addPublicKeyIdentity(keyPair);
                AuthFuture authFuture = session.auth();
                if (authFuture.verify(sshProperties.getConnectionTimeout()).isSuccess()) {
                    return true;
                }
            }
        }

        return false;
    }

    private List<KeyPair> loadKeyPairs(SshConnectionConfig config)
            throws IOException, GeneralSecurityException {

        InputStream keyStream;
        String resourceName;

        if (StringUtils.hasText(config.getPrivateKeyContent())) {
            keyStream = new ByteArrayInputStream(config.getPrivateKeyContent().getBytes());
            resourceName = "private_key_content";
        } else {
            keyStream = Files.newInputStream(Paths.get(config.getPrivateKeyPath()));
            resourceName = config.getPrivateKeyPath();
        }

        try (InputStream in = keyStream) {
            Iterable<KeyPair> keyPairs = SecurityUtils.loadKeyPairIdentities(
                    null,
                    NamedResource.ofName(resourceName),
                    in,
                    config.getPasswordProvider()
            );

            return keyPairs != null ? StreamSupport.stream(keyPairs.spliterator(), false)
                    .collect(Collectors.toList())
                    : Collections.emptyList();
        }
    }

    private String createTempScript(ClientSession session, ScriptExecutionRequest request)
            throws IOException {

        String scriptPath = "/tmp/script_" + System.currentTimeMillis() +
                request.getScriptType().getExtension();

        // 创建脚本文件
        String createCommand = String.format("cat > %s << 'EOF'\n%s\nEOF",
                scriptPath, request.getScriptContent());

        try (ChannelExec channel = session.createExecChannel(createCommand)) {
            channel.open().verify(sshProperties.getConnectionTimeout());
            channel.waitFor(EnumSet.of(ClientChannelEvent.CLOSED),
                    sshProperties.getExecutionTimeout());
        }

        // 设置执行权限
        String chmodCommand = "chmod +x " + scriptPath;
        try (ChannelExec channel = session.createExecChannel(chmodCommand)) {
            channel.open().verify(sshProperties.getConnectionTimeout());
            channel.waitFor(EnumSet.of(ClientChannelEvent.CLOSED),
                    sshProperties.getExecutionTimeout());
        }

        return scriptPath;
    }

    private RemoteExecutionResult executeRemoteScript(ClientSession session,
                                                      String scriptPath,
                                                      ScriptExecutionRequest request)
            throws IOException {

        StringBuilder commandBuilder = new StringBuilder();

        // 设置环境变量
        if (request.getEnvironment() != null) {
            request.getEnvironment().forEach((key, value) ->
                    commandBuilder.append("export ").append(key).append("=").append(value).append("; "));
        }

        // 切换工作目录
        if (StringUtils.hasText(request.getWorkingDirectory())) {
            commandBuilder.append("cd ").append(request.getWorkingDirectory()).append("; ");
        }

        // 执行脚本
        commandBuilder.append(request.getScriptType().getInterpreter())
                .append(" ").append(scriptPath);

        String command = commandBuilder.toString();

        try (ChannelExec channelExec = session.createExecChannel(command)) {
            ByteArrayOutputStream stdout = new ByteArrayOutputStream();
            ByteArrayOutputStream stderr = new ByteArrayOutputStream();
            channelExec.setOut(stdout);
            channelExec.setErr(stderr);

            long startTime = System.currentTimeMillis();
            channelExec.open().verify(sshProperties.getConnectionTimeout());

            int timeout = request.getTimeoutSeconds() > 0 ?
                    request.getTimeoutSeconds() * 1000 :
                    sshProperties.getExecutionTimeout();

            channelExec.waitFor(EnumSet.of(ClientChannelEvent.CLOSED), timeout);

            Integer exitCode = channelExec.getExitStatus();
            long executionTime = System.currentTimeMillis() - startTime;

            return RemoteExecutionResult.builder()
                    .exitCode(exitCode != null ? exitCode : -1)
                    .stdout(stdout.toString("UTF-8"))
                    .stderr(stderr.toString("UTF-8"))
                    .success(exitCode != null && exitCode == 0)
                    .executionTime(executionTime)
                    .build();
        }
    }

    private void cleanupTempScript(ClientSession session, String scriptPath) {
        try (ChannelExec channel = session.createExecChannel("rm -f " + scriptPath)) {
            channel.open().verify(sshProperties.getConnectionTimeout());
            channel.waitFor(EnumSet.of(ClientChannelEvent.CLOSED), 5000);
        } catch (Exception e) {
            log.warn("Failed to clean up temporary script file: {}", scriptPath, e);
        }
    }
}
