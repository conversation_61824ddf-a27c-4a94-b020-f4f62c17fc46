package com.cmpay.hacp.inspection.application.service;

import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionResult;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskExecutionStatusDO;

public interface InspectionTaskMonitoringService {
    void initializeTaskExecution(String taskId, String taskName, TriggerMode triggerMode, Integer ruleTotal);

    void markTaskAsRunning(String taskId);

    void markTaskAsCompleted(String taskId, boolean success);

    void recordRuleExecutionResult(String taskId, String ruleId, String pluginId, InspectionResult result);

    TaskExecutionStatusDO getTaskExecutionStatus(String taskId);
}
