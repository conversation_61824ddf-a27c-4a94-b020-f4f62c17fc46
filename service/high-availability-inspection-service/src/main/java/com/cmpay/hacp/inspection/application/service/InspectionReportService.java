package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.InspectionTaskReport;
import com.cmpay.hacp.inspection.domain.model.report.ResourceInspectionReport;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 巡检报告服务接口
 */
public interface InspectionReportService {
    
    /**
     * 获取任务执行报告
     * @param taskId 任务ID
     * @param executionId 执行ID，如果为null则获取最新一次执行
     * @return 任务执行报告
     */
    InspectionTaskReport getTaskExecutionReport(String taskId, Long executionId);
    
    /**
     * 获取资源巡检报告
     * @param resourceId 资源ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 资源巡检报告
     */
    ResourceInspectionReport getResourceInspectionReport(String resourceId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 获取按日巡检报告
     * @param date 日期
     * @return 按日巡检报告
     */
    DailyInspectionReport getDailyInspectionReport(LocalDate date);
    
    /**
     * 获取日期范围内的巡检报告摘要
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param page 分页参数
     * @return 巡检报告摘要列表
     */
    IPage<Map<String, Object>> getInspectionReportSummary(LocalDate startDate, LocalDate endDate, IPage<?> page);
    
    /**
     * 导出巡检报告
     * @param taskId 任务ID
     * @param executionId 执行ID
     * @param format 导出格式 (PDF, EXCEL, HTML)
     * @return 导出文件路径
     */
    String exportTaskReport(String taskId, Long executionId, String format);
    
    /**
     * 导出按日巡检报告
     * @param date 日期
     * @param format 导出格式 (PDF, EXCEL, HTML)
     * @return 导出文件路径
     */
    String exportDailyReport(LocalDate date, String format);
}
