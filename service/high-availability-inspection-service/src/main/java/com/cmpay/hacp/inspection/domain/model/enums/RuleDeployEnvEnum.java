package com.cmpay.hacp.inspection.domain.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RuleDeployEnvEnum {

    VM(1, "虚拟机(部署在虚拟机上的服务)"),
    CONTAINER(2, "容器(部署在K8s/Docker环境的服务)");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, RuleDeployEnvEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleDeployEnvEnum::getCode, type -> type));


    RuleDeployEnvEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RuleDeployEnvEnum getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
}

