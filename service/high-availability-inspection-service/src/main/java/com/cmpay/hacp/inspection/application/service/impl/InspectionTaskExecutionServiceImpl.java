package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cmpay.hacp.inspection.application.service.InspectionTaskExecutionService;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.InspectionTaskDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleDO;
import com.cmpay.hacp.inspection.infrastructure.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 巡检执行服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionTaskExecutionServiceImpl implements InspectionTaskExecutionService {
    private final TaskRepository taskRepository;
    private final TaskRuleRepository taskRuleRepository;
    private final InspectionTaskMonitoringServiceImpl monitoringService;
    private final AsyncTaskExecutionServiceImpl asyncTaskExecutionService;

    @Override
    public void executeTask(String taskId, TriggerMode triggerMode) {
        log.info("Starting task execution for taskId: {}", taskId);

        // 获取任务信息
        InspectionTaskDO taskDO = taskRepository.getOne(
                Wrappers.lambdaQuery(InspectionTaskDO.class)
                        .eq(InspectionTaskDO::getTaskId, taskId));

        if (taskDO == null) {
            log.error("Task not found: {}", taskId);
            return;
        }

        // 获取任务关联的规则列表
        List<TaskRuleDO> taskRuleDOList = taskRuleRepository.list(
                Wrappers.lambdaQuery(TaskRuleDO.class)
                        .eq(TaskRuleDO::getTaskId, taskId));

        if (taskRuleDOList.isEmpty()) {
            log.warn("No rules found for task: {}", taskId);
            return;
        }

        // 初始化任务执行状态
        monitoringService.initializeTaskExecution(taskId, taskDO.getName(), triggerMode, taskRuleDOList.size());
        monitoringService.markTaskAsRunning(taskId);

        // 异步执行任务
        asyncTaskExecutionService.executeTaskAsync(taskId, taskRuleDOList);

        log.info("Task execution initiated asynchronously: {}", taskId);
    }

}
