package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.RuleConditionDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.RulePluginResultMapper;
import org.springframework.stereotype.Repository;

@Repository
public class RulePluginResultRepository extends CrudRepository<RulePluginResultMapper, RuleConditionDO> {
}
