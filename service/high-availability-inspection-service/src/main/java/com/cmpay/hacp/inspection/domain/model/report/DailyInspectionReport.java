package com.cmpay.hacp.inspection.domain.model.report;

import com.cmpay.hacp.inspection.domain.model.enums.ExecutionResult;
import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 按日巡检报告
 */
@Data
public class DailyInspectionReport {
    /**
     * 报告日期
     */
    private LocalDate reportDate;
    
    /**
     * 任务执行总数
     */
    private Integer totalExecutions;
    
    /**
     * 成功执行数
     */
    private Integer successExecutions;
    
    /**
     * 失败执行数
     */
    private Integer failedExecutions;
    
    /**
     * 规则执行总数
     */
    private Integer totalRuleExecutions;
    
    /**
     * 规则成功数
     */
    private Integer successRuleExecutions;
    
    /**
     * 规则失败数
     */
    private Integer failedRuleExecutions;
    
    /**
     * 平均规则通过率(%)
     */
    private Double averageSuccessRate;
    
    /**
     * 平均资源覆盖率(%)
     */
    private Double averageCoverageRate;
    
    /**
     * 任务执行摘要列表
     */
    private List<TaskExecutionSummary> taskExecutions;
    
    /**
     * 资源健康状况统计
     * key: 资源类型
     * value: 健康状况统计
     */
    private Map<String, ResourceHealthStats> resourceHealthByType;
    
    /**
     * 任务执行摘要
     */
    @Data
    public static class TaskExecutionSummary {
        /**
         * 任务ID
         */
        private String taskId;
        
        /**
         * 任务名称
         */
        private String taskName;
        
        /**
         * 执行ID
         */
        private Long executionId;
        
        /**
         * 执行状态
         */
        private ExecutionStatus executionStatus;
        
        /**
         * 执行结果
         */
        private ExecutionResult executionResult;
        
        /**
         * 规则通过率(%)
         */
        private Integer successRate;
        
        /**
         * 结果概要
         */
        private String resultSummary;
    }
    
    /**
     * 资源健康状况统计
     */
    @Data
    public static class ResourceHealthStats {
        /**
         * 健康资源数
         */
        private Integer healthyCount;
        
        /**
         * 异常资源数
         */
        private Integer unhealthyCount;
        
        /**
         * 健康率(%)
         */
        private Double healthRate;
    }
}
