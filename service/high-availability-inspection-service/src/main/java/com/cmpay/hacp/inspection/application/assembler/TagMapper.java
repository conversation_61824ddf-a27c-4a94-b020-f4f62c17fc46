package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.common.Tag;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TagDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TagMapper {
    List<Tag> toTagList(List<TagDO> tagDOList);

    @Mapping(target = "tagId", source = "id")
    Tag toTarget(TagDO source);
}
