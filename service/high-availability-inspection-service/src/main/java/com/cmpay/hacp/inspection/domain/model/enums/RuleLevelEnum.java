package com.cmpay.hacp.inspection.domain.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RuleLevelEnum {

    LOW(1, "低"),
    ME<PERSON>UM(2, "中"),
    <PERSON><PERSON><PERSON>(3, "高"),
    <PERSON><PERSON><PERSON><PERSON>(4, "严重");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, RuleLevelEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleLevelEnum::getCode, type -> type));


    RuleLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RuleLevelEnum getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
}
