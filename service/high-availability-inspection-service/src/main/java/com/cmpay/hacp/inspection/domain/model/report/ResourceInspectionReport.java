package com.cmpay.hacp.inspection.domain.model.report;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 资源巡检报告
 */
@Data
public class ResourceInspectionReport {
    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 巡检次数
     */
    private Integer inspectionCount;

    /**
     * 规则执行总数
     */
    private Integer ruleExecutionCount;

    /**
     * 规则成功数
     */
    private Integer ruleSuccessCount;

    /**
     * 规则失败数
     */
    private Integer ruleFailCount;

    /**
     * 平均规则通过率(%)
     */
    private Double averageSuccessRate;

    /**
     * 健康状态趋势
     * key: 日期
     * value: 健康率(%)
     */
    private Map<LocalDate, Double> healthTrend;

    /**
     * 规则执行详情列表
     */
    private List<ResourceRuleExecution> taskRuleExecutions;

    /**
     * 资源规则执行详情
     */
    @Data
    public static class ResourceRuleExecution {
        /**
         * 规则ID
         */
        private String ruleId;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 任务ID
         */
        private String taskId;

        /**
         * 任务名称
         */
        private String taskName;

        /**
         * 执行时间
         */
        private LocalDateTime executionTime;

        /**
         * 执行状态 0:失败 1:成功
         */
        private String executionStatus;

        /**
         * 执行结果详情
         */
        private String executionResult;

        /**
         * 插件名称
         */
        private String pluginName;
    }
}
