package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.assembler.InspectionRuleMapper;
import com.cmpay.hacp.inspection.application.service.InspectionRuleService;
import com.cmpay.hacp.inspection.domain.model.enums.RuleAngleViewEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleDeployEnvEnum;
import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.domain.model.rule.RulePluginParam;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.*;
import com.cmpay.hacp.inspection.infrastructure.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionRuleServiceImpl implements InspectionRuleService {

    private final RuleRepository ruleRepository;

    private final RuleTagsRepository ruleTagsRepository;

    private final InspectionRuleMapper inspectionRuleMapper;

    private final RulePluginMappingRepository rulePluginMappingRepository;
    private final RuleTargetRepository ruleTargetRepository;
    private final RuleConditionRepository ruleConditionRepository;
    private final RulePluginParamRepository rulePluginParamRepository;


    @Override
    public IPage<InspectionRule> getRulePage(PageDTO<?> page, InspectionRule queryCondition) {
        // 参数校验：处理page为null的情况，并设置默认分页参数
        if (page == null) {
            page = new PageDTO<>();
            page.setCurrent(1);
            page.setSize(10); // 默认每页10条
        }

        // 转换查询条件对象
        InspectionRuleDO queryDO = inspectionRuleMapper.toInspectionRuleDO(queryCondition);

        // 构建MyBatis-Plus分页对象
        Page<InspectionRuleDO> mpPage = new Page<>(page.getCurrent(), page.getSize());

        // 构造查询条件
        LambdaQueryWrapper<InspectionRuleDO> queryWrapper = Wrappers.lambdaQuery(InspectionRuleDO.class)
                .eq(queryDO.getId() != null, InspectionRuleDO::getId, queryDO.getId())
                .like(StringUtils.isNotBlank(queryDO.getName()), InspectionRuleDO::getName, queryDO.getName())
                .eq(queryDO.getLevel() != null, InspectionRuleDO::getLevel, queryDO.getLevel())
                .eq(queryDO.getStatus() != null, InspectionRuleDO::getStatus, queryDO.getStatus())
                .eq(queryDO.getType() != null, InspectionRuleDO::getType, queryDO.getType())
                .orderByAsc(InspectionRuleDO::getId);

        // 执行分页查询
        IPage<InspectionRuleDO> ruleDOPage = ruleRepository.page(mpPage, queryWrapper);

        // 转换结果为领域对象
        IPage<InspectionRule> ruleIPage = ruleDOPage.convert(inspectionRuleMapper::toInspectionRule);

        // 处理关联数据
        if (!ruleIPage.getRecords().isEmpty()) {
            enrichInspectionRules(ruleIPage.getRecords());
        }

        return ruleIPage;
    }

    private void enrichInspectionRules(List<InspectionRule> records) {
        List<String> ruleIds = records.stream()
                .map(InspectionRule::getRuleId)
                .collect(Collectors.toList());

        // 批量查询关联数据
        List<RuleTagsDO> tagsDOS = ruleTagsRepository.list(
                Wrappers.lambdaQuery(RuleTagsDO.class).in(RuleTagsDO::getRuleId, ruleIds));

        List<RulePluginMappingDO> mappingDOS = rulePluginMappingRepository.list(
                Wrappers.lambdaQuery(RulePluginMappingDO.class).in(RulePluginMappingDO::getRuleId, ruleIds));

        List<RuleTargetDO> targetDOS = ruleTargetRepository.list(
                Wrappers.lambdaQuery(RuleTargetDO.class).in(RuleTargetDO::getRuleId, ruleIds));

        List<RuleConditionDO> pluginResultDOS = ruleConditionRepository.list(
                Wrappers.lambdaQuery(RuleConditionDO.class).in(RuleConditionDO::getRuleId, ruleIds));

        List<RulePluginParamDO> pluginParamDOS = rulePluginParamRepository.list(
                Wrappers.lambdaQuery(RulePluginParamDO.class).in(RulePluginParamDO::getRuleId, ruleIds)
        );

        // 构建关联数据映射
        Map<String, List<RuleTagsDO>> tagsMap = tagsDOS.stream()
                .collect(Collectors.groupingBy(RuleTagsDO::getRuleId));

        Map<String, RulePluginMappingDO> pluginMap = mappingDOS.stream()
                .collect(Collectors.toMap(RulePluginMappingDO::getRuleId, Function.identity()));

        Map<String, RuleTargetDO> targetMap = targetDOS.stream()
                .collect(Collectors.toMap(RuleTargetDO::getRuleId, Function.identity()));

        Map<String, RuleConditionDO> pluginResultMap = pluginResultDOS.stream()
                .collect(Collectors.toMap(RuleConditionDO::getRuleId, Function.identity()));

        Map<String, List<RulePluginParamDO>> pluginParamMap = pluginParamDOS.stream()
                .collect(Collectors.groupingBy(RulePluginParamDO::getRuleId));

        // 填充关联数据
        records.forEach(record -> {
            String ruleId = record.getRuleId();

            // 设置插件ID
            Optional.ofNullable(pluginMap.get(ruleId))
                    .ifPresent(mapping -> record.setPluginId(mapping.getPluginId()));

            // 设置目标参数
            Optional.ofNullable(targetMap.get(ruleId))
                    .ifPresent(target -> {
                        record.setAngleView(RuleAngleViewEnum.getByCode(target.getAngleView()));
                        record.setDeployEnv(RuleDeployEnvEnum.getByCode(target.getDeployEnv()));
                    });

            // 设置标签ID列表
            List<Long> tagIds = Optional.ofNullable(tagsMap.get(ruleId))
                    .map(list -> list.stream().map(RuleTagsDO::getTagId).collect(Collectors.toList()))
                    .orElse(Collections.emptyList());
            record.setTagIds(tagIds);

            // 设置执行规则
            Optional.ofNullable(pluginResultMap.get(ruleId))
                    .ifPresent(pluginResultDO -> {
                        record.setPluginResult(inspectionRuleMapper.toRuleTriggerCondition(pluginResultDO));
                    });

            // 设置规则插件参数列表
            List<RulePluginParam> pluginParams = Optional.ofNullable(pluginParamMap.get(ruleId))
                    .map(list -> list.stream()
                            .map(inspectionRuleMapper::toRulePluginParam)  // 添加DO到DTO的转换
                            .collect(Collectors.toList()))
                    .orElse(Collections.emptyList());

            record.setPluginParams(pluginParams);

        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRule(InspectionRule inspectionRule) {
        // 保存插件基本信息
        InspectionRuleDO inspectionRuleDO = inspectionRuleMapper.toInspectionRuleDO(inspectionRule);
        inspectionRuleDO.setRuleId("TEMP_Rule"+System.currentTimeMillis());
        ruleRepository.save(inspectionRuleDO);
        // 业务标识符
        String ruleId = String.format("RULE-%06d", inspectionRuleDO.getId());
        inspectionRuleDO.setRuleId(ruleId);
        ruleRepository.update(Wrappers.lambdaUpdate(InspectionRuleDO.class)
                .eq(InspectionRuleDO::getId, inspectionRuleDO.getId())
                .set(InspectionRuleDO::getRuleId, inspectionRuleDO.getRuleId()));

        inspectionRule.setPluginId(ruleId);

        // 如果有标签，保存标签关联
        if (CollectionUtils.isNotEmpty(inspectionRule.getTagIds())) {
            // 保存新标签关联
            List<RuleTagsDO> mappings = new ArrayList<>();
            for (Long tagId : inspectionRule.getTagIds()) {
                RuleTagsDO mapping = new RuleTagsDO();
                mapping.setRuleId(inspectionRuleDO.getRuleId());
                mapping.setTagId(tagId);
                mappings.add(mapping);
            }

            ruleTagsRepository.saveBatch(mappings, 10);
        }

        // 保存规则插件关联表
        RulePluginMappingDO rulePluginMappingDO = new RulePluginMappingDO();
        rulePluginMappingDO.setRuleId(inspectionRuleDO.getRuleId());
        rulePluginMappingDO.setPluginId(inspectionRule.getPluginId());
        rulePluginMappingRepository.save(rulePluginMappingDO);

        // 保存规则目标范围表
        RuleTargetDO ruleTargetDO = new RuleTargetDO();
        ruleTargetDO.setRuleId(inspectionRuleDO.getRuleId());
        ruleTargetDO.setAngleView(inspectionRule.getAngleView().getCode());
        ruleTargetDO.setDeployEnv(inspectionRule.getDeployEnv().getCode());
        ruleTargetRepository.save(ruleTargetDO);

        // 保存规则监控字段
        if (ObjectUtils.isNotNull(inspectionRule.getPluginResult())) {
            RuleConditionDO conditionDO = new RuleConditionDO();
            conditionDO.setRuleId(inspectionRuleDO.getRuleId());
            BeanUtils.copyProperties(inspectionRule.getPluginResult(), conditionDO);
            ruleConditionRepository.save(conditionDO);
        }

        if (CollectionUtils.isNotEmpty(inspectionRule.getPluginParams())) {
            List<RulePluginParamDO> results = inspectionRule.getPluginParams().stream()
                    .map(scriptResult -> inspectionRuleMapper.toRulePluginParamDO(scriptResult, ruleId))
                    .collect(Collectors.toList());
            rulePluginParamRepository.saveBatch(results, 10);
        }

        return ruleId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRule(InspectionRule inspectionRule) {
        log.info("Updating rule with ID: {}", inspectionRule.getRuleId());

        // 1. 更新规则的基本信息
        InspectionRuleDO inspectionRuleDO = inspectionRuleMapper.toInspectionRuleDO(inspectionRule);
        inspectionRuleDO.setRuleId(inspectionRule.getRuleId());
        boolean updateRule = ruleRepository.update(Wrappers.lambdaUpdate(InspectionRuleDO.class)
                .eq(InspectionRuleDO::getRuleId, inspectionRuleDO.getRuleId())
                .set(InspectionRuleDO::getName, inspectionRuleDO.getName())
                .set(InspectionRuleDO::getDescription, inspectionRuleDO.getDescription())
                .set(InspectionRuleDO::getLevel, inspectionRuleDO.getLevel())
                .set(InspectionRuleDO::getStatus, inspectionRuleDO.getStatus())
                .set(InspectionRuleDO::getType, inspectionRuleDO.getType()));

        if (!updateRule) {
            log.error("Failed to update rule with ID: {}", inspectionRule.getRuleId());
            return false;
        }

        // 2.更新标签关联
        if (CollectionUtils.isEmpty(inspectionRule.getTagIds())) {
            // 如果标签为空，直接删除所有关联
            ruleTagsRepository.remove(
                    Wrappers.lambdaQuery(RuleTagsDO.class)
                            .eq(RuleTagsDO::getRuleId, inspectionRule.getRuleId()));
        } else {
            // 获取原有标签关联
            List<RuleTagsDO> originList = ruleTagsRepository.list(
                    Wrappers.lambdaQuery(RuleTagsDO.class)
                            .eq(RuleTagsDO::getRuleId, inspectionRule.getRuleId()));

            // 将原有标签ID转换为Set提高查找效率
            Set<Long> existingTagIds = originList.stream()
                    .map(RuleTagsDO::getTagId)
                    .collect(Collectors.toSet());

            // 将需要保留的标签ID转为Set
            Set<Long> retainedTagIds = new HashSet<>(inspectionRule.getTagIds());

            // 准备插入列表 - 找出新增的标签ID
            List<RuleTagsDO> tagsToInsert = new ArrayList<>();
            for (Long tagId : retainedTagIds) {
                if (!existingTagIds.contains(tagId)) {
                    RuleTagsDO newTag = new RuleTagsDO();
                    newTag.setRuleId(inspectionRule.getRuleId());
                    newTag.setTagId(tagId);
                    tagsToInsert.add(newTag);
                }
            }

            // 因标签关联对象只有规则ID和标签ID，所以更新列表无需更新

            // 准备删除列表 - 找出需要删除的标签
            List<Long> idsToDelete = new ArrayList<>();
            for (RuleTagsDO existingTag : originList) {
                if (!retainedTagIds.contains(existingTag.getTagId())) {
                    idsToDelete.add(existingTag.getId());
                }
            }

            if (!tagsToInsert.isEmpty()) {
                ruleTagsRepository.saveBatch(tagsToInsert, 10);
            }
            if (!idsToDelete.isEmpty()) {
                ruleTagsRepository.removeByIds(idsToDelete);
            }
        }

        // 修改规则插件关联表
        rulePluginMappingRepository.update(Wrappers.lambdaUpdate(RulePluginMappingDO.class)
                .eq(RulePluginMappingDO::getRuleId, inspectionRuleDO.getRuleId())
                .eq(RulePluginMappingDO::getPluginId, inspectionRule.getPluginId()));

        // 修改规则目标范围表
        ruleTargetRepository.update(Wrappers.lambdaUpdate(RuleTargetDO.class)
                .eq(RuleTargetDO::getRuleId, inspectionRuleDO.getRuleId())
                .eq(RuleTargetDO::getAngleView, inspectionRule.getAngleView())
                .eq(RuleTargetDO::getDeployEnv, inspectionRule.getDeployEnv()));

        // 修改规则配置
        if (ObjectUtils.isNotNull(inspectionRule.getPluginResult())) {
            ruleConditionRepository.update(Wrappers.lambdaUpdate(RuleConditionDO.class)
                    .eq(RuleConditionDO::getRuleId, inspectionRule.getRuleId())
                    .set(RuleConditionDO::getPluginId, inspectionRule.getPluginId())
                    .set(RuleConditionDO::getOutputFiledId, inspectionRule.getPluginResult().getPluginResultId())
                    .set(RuleConditionDO::getComparisonOperator, inspectionRule.getPluginResult().getJudge())
                    .set(RuleConditionDO::getComparisonValue, inspectionRule.getPluginResult().getJudgeValue())
                    .set(RuleConditionDO::getDuration, inspectionRule.getPluginResult().getDuration())
                    .set(RuleConditionDO::getCheckInterval, inspectionRule.getPluginResult().getCheckInterval())
                    .set(RuleConditionDO::getCheckPeak, inspectionRule.getPluginResult().getCheckPeak())
                    .set(RuleConditionDO::getSpecificProcesses, inspectionRule.getPluginResult().getSpecificProcesses())
                    .set(RuleConditionDO::getRulePreview, inspectionRule.getPluginResult().getRulePreview())
                    .set(RuleConditionDO::getSuggestion, inspectionRule.getPluginResult().getSuggest()));
        }

        if (CollectionUtils.isNotEmpty(inspectionRule.getPluginParams())) {
            List<RulePluginParamDO> results = inspectionRule.getPluginParams().stream()
                    .map(scriptResult -> inspectionRuleMapper.toRulePluginParamDO(scriptResult, inspectionRule.getRuleId()))
                    .collect(Collectors.toList());
            rulePluginParamRepository.updateBatchById(results, 10);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRule(String ruleId) {
        // 删除基本信息
        ruleRepository.remove(
                Wrappers.lambdaQuery(InspectionRuleDO.class)
                        .eq(InspectionRuleDO::getRuleId, ruleId));

        // 删除相关信息
        ruleTagsRepository.remove(
                Wrappers.lambdaQuery(RuleTagsDO.class)
                .eq(RuleTagsDO::getRuleId, ruleId)
        );
        rulePluginMappingRepository.remove(
                Wrappers.lambdaQuery(RulePluginMappingDO.class)
                .eq(RulePluginMappingDO::getRuleId, ruleId)
        );
        ruleTargetRepository.remove(
                Wrappers.lambdaQuery(RuleTargetDO.class)
                .eq(RuleTargetDO::getRuleId, ruleId)
        );
        ruleConditionRepository.remove(
                Wrappers.lambdaQuery(RuleConditionDO.class)
                        .eq(RuleConditionDO::getRuleId, ruleId)
        );
        rulePluginParamRepository.remove(
                Wrappers.lambdaQuery(RulePluginParamDO.class)
                .eq(RulePluginParamDO::getRuleId, ruleId)
        );
    }

    @Override
    public InspectionRule getRuleDetail(String ruleId) {
        // 基本信息
        InspectionRuleDO inspectionRuleDO = ruleRepository.getOne(
                Wrappers.lambdaQuery(InspectionRuleDO.class)
                        .eq(InspectionRuleDO::getRuleId, ruleId));

        if (inspectionRuleDO == null) {
            return null;
        }

        // 相关信息
        List<RuleTagsDO> tagsDOS = ruleTagsRepository.list(
                Wrappers.lambdaQuery(RuleTagsDO.class)
                        .eq(RuleTagsDO::getRuleId, ruleId)
        );
        RulePluginMappingDO pluginMappingDO = rulePluginMappingRepository.getOne(
                Wrappers.lambdaQuery(RulePluginMappingDO.class)
                        .eq(RulePluginMappingDO::getRuleId, ruleId)
        );
        RuleTargetDO targetDO = ruleTargetRepository.getOne(
                Wrappers.lambdaQuery(RuleTargetDO.class)
                        .eq(RuleTargetDO::getRuleId, ruleId)
        );
        RuleConditionDO pluginResultDO = ruleConditionRepository.getOne(
                Wrappers.lambdaQuery(RuleConditionDO.class)
                        .eq(RuleConditionDO::getRuleId, ruleId)
        );
        List<RulePluginParamDO> pluginParams = rulePluginParamRepository.list(
                Wrappers.lambdaQuery(RulePluginParamDO.class)
                        .eq(RulePluginParamDO::getRuleId, ruleId)
        );

        InspectionRule inspectionRule = inspectionRuleMapper.toInspectionRule(inspectionRuleDO, pluginMappingDO, targetDO, pluginResultDO, pluginParams);
        inspectionRule.setTagIds(tagsDOS.stream().map(RuleTagsDO::getTagId).collect(Collectors.toList()));

        return inspectionRule;
    }


}
