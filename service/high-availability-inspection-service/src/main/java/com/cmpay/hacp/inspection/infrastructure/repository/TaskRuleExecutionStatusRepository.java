package com.cmpay.hacp.inspection.infrastructure.repository;

import com.baomidou.mybatisplus.extension.repository.CrudRepository;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.TaskRuleExecutionStatusDO;
import com.cmpay.hacp.inspection.infrastructure.mapper.TaskRuleExecutionStatusMapper;
import org.springframework.stereotype.Repository;

/**
 * 规则执行状态Repository
 */
@Repository
public class TaskRuleExecutionStatusRepository extends CrudRepository<TaskRuleExecutionStatusMapper, TaskRuleExecutionStatusDO> {
}
