package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.rule.InspectionRule;
import com.cmpay.hacp.inspection.domain.model.rule.RulePluginParam;
import com.cmpay.hacp.inspection.domain.model.rule.RulePluginResult;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 巡检规则对象转换器
 * 用于领域对象和实体对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface InspectionRuleMapper {

    @IgnoreAuditFields
    @Mapping(target = "ruleId", ignore = true)
    InspectionRuleDO toInspectionRuleDO(InspectionRule queryCondition);

    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "pluginId", ignore = true)
    @Mapping(target = "angleView", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "pluginParams", ignore = true)
    @Mapping(target = "pluginResult", ignore = true)
    @Mapping(target = "auditInfo.createdBy", source = "inspectionRuleDO.createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "inspectionRuleDO.createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "inspectionRuleDO.updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "inspectionRuleDO.updatedTime")
    InspectionRule toInspectionRule(InspectionRuleDO inspectionRuleDO);

    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "auditInfo.createdBy", source = "inspectionRuleDO.createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "inspectionRuleDO.createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "inspectionRuleDO.updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "inspectionRuleDO.updatedTime")
    @Mapping(target = "ruleId", source = "inspectionRuleDO.ruleId")
    @Mapping(target = "pluginId", source = "pluginMappingDO.pluginId")
    @Mapping(target = "angleView", source = "targetDO.angleView")
    @Mapping(target = "deployEnv", source = "targetDO.deployEnv")
    @Mapping(target = "pluginResult", source = "pluginResultDO")
    InspectionRule toInspectionRule(
            InspectionRuleDO inspectionRuleDO,
            RulePluginMappingDO pluginMappingDO,
            RuleTargetDO targetDO,
            RuleConditionDO pluginResultDO,
            List<RulePluginParamDO> pluginParams
    );

    RulePluginResult toRuleTriggerCondition(RuleConditionDO ruleConditionDO);

    @IgnoreAuditFields
    RulePluginParamDO toRulePluginParamDO(RulePluginParam scriptResult, String ruleId);

    RulePluginParam toRulePluginParam(RulePluginParamDO rulePluginParamDO);

}
