package com.cmpay.hacp.inspection.domain.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ExecutionStatus {
    PENDING(0, "待执行"),
    RUNNING(1, "执行中"),
    COMPLETED(2, "已完成"),
    FAILED(3, "失败"),
    STOPPED(4, "已停止");

    @JsonValue
    private final Integer code;
    private final String desc;

    ExecutionStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
