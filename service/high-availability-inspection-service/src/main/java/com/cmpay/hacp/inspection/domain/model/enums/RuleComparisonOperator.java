package com.cmpay.hacp.inspection.domain.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum RuleComparisonOperator {
    GREATER_THAN(1, "大于"),
    LESS_THAN(2, "小于"),
    GREATER_THAN_OR_EQUAL(3, "大于等于"),
    LESS_THAN_OR_EQUAL(4, "小于等于"),
    EQUAL(5, "等于");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, RuleComparisonOperator> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleComparisonOperator::getCode, type -> type));


    RuleComparisonOperator(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RuleComparisonOperator getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
}
