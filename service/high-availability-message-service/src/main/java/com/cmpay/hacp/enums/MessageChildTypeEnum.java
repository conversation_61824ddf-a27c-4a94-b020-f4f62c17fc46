package com.cmpay.hacp.enums;

import com.cmpay.lemon.framework.valuable.Valuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/09/14 14:34
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum MessageChildTypeEnum implements Valuable<String> {

    OTHER("other", "其他"),
    ANNOUNCEMENT("announcement", "公告"),
    EMERGENCY_APPROVAL("emergency_approval", "预案审批")

    ;

    private final String value;

    private final String desc;
}
