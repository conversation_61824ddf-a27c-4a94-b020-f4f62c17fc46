package com.cmpay.hacp.bo;

import com.cmpay.hacp.enums.MessageChildTypeEnum;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2024/09/14 14:16
 * @since 1.0.0
 */
@Data
@Slf4j
@EqualsAndHashCode(callSuper = true)
public class InteriorMessageBO extends BaseMessageBO {

    private String operationId;

    private String operationName;

    private MessageChildTypeEnum messageChildType;

    @Override
    public void checkParams() {
        if (JudgeUtils.isNullAny(this.getMessageTitle(), this.getMessageContent(), this.getOperationId(), this.getOperationName(), this.getMessageChildType())) {
            log.error("参数为空,title:{},content:{},operationId:{},operationName:{},messageChildType:{}", this.getMessageTitle(), this.getMessageContent(), this.getOperationId(), this.getOperationName(), this.getMessageChildType());
            BusinessException.throwBusinessException(MsgEnum.PARAM_IS_NULL_ERROR);
        }
    }

}
