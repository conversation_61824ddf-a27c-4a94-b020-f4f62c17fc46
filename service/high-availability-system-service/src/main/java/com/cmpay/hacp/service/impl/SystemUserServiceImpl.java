package com.cmpay.hacp.service.impl;

import com.cmpay.hacp.bo.system.RoleBO;
import com.cmpay.hacp.bo.system.UserBO;
import com.cmpay.hacp.bo.system.UserInfoBO;
import com.cmpay.hacp.dao.ILoginLatestInfoExtDao;
import com.cmpay.hacp.dao.IUserExtDao;
import com.cmpay.hacp.dao.IUserRoleExtDao;
import com.cmpay.hacp.entity.LoginLatestInfoDO;
import com.cmpay.hacp.entity.RoleDO;
import com.cmpay.hacp.entity.UserDO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.enums.StatusConstans;
import com.cmpay.hacp.service.SystemAccessTokenService;
import com.cmpay.hacp.service.SystemDepartmentService;
import com.cmpay.hacp.service.SystemDictionaryService;
import com.cmpay.hacp.service.SystemUserService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.hacp.utils.crypto.PasswordUtil;
import com.cmpay.hacp.utils.crypto.SM2EncryptorUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.DateTimeUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SystemUserServiceImpl implements SystemUserService {

    @Autowired
    public SystemDepartmentService systemDepartmentService;

    @Autowired
    public SystemDictionaryService systemDictionaryService;

    @Autowired
    public IUserExtDao userExtDao;

    @Autowired
    public IUserRoleExtDao userRoleExtDao;

    @Resource
    private ILoginLatestInfoExtDao loginLatestInfoExtDao;

    @Autowired
    public SystemAccessTokenService systemAccessTokenService;


    /**
     * 应用 ID
     */
    @Value("${spring.application.name:}")
    private String appId;

    /**
     * 查询用户列表
     *
     * @param userInfoBO
     * @return
     */
    @Override
    public PageInfo<UserBO> getUsersByPage(Integer pageSize, Integer pageNum, UserInfoBO userInfoBO) {
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userInfoBO);
        List<String> deptIds = new ArrayList<>();
        //是否是管理员，否则只能查询本部门及以下部门的用户
        if (!systemDictionaryService.isAdmin(userInfoBO.getOperatorId(), appId)) {
            deptIds = systemDepartmentService.getDeptIds(userInfoBO.getOperatorId());
        }
        PageInfo<UserBO> pageInfo;
        if (pageNum == 0 || pageSize == 0) {
            pageInfo = new PageInfo<>(userExtDao.queryUsers(userDO, deptIds));
        } else {
            List<String> finalDeptIds = deptIds;
            pageInfo = PageUtils.pageQueryWithCount(pageNum, pageSize, () -> userExtDao.queryUsers(userDO, finalDeptIds));
        }
        return pageInfo;
    }

    @Override
    public UserBO getUserInfo(String userId) {
        UserBO userBO = new UserBO();
        UserDO userDO = userExtDao.get(userId);
        if (JudgeUtils.isNull(userDO)) {
            return userBO;
        }
        BeanUtils.copyProperties(userBO, userDO);
        userBO.setPassword(null);
        List<RoleDO> roleDOS = userRoleExtDao.getRolesByUserId(userId);
        if (JudgeUtils.isNotEmpty(roleDOS)) {
            userBO.setRoleList(BeanConvertUtil.convertList(roleDOS, RoleBO.class));
        }
        return userBO;
    }

    @Override
    public List<UserBO> getUsersByUserId(List<String> userIds) {
        if(JudgeUtils.isEmpty(userIds)){
            BusinessException.throwBusinessException(MsgEnum.ID_NOT_NULL);
        }
        return userExtDao.getUsersByUserId(userIds);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void add(String operatorId, UserBO userBO, List<Long> roleIdList) {
        String hasRole = getHasRole(operatorId, userBO, roleIdList);
        this.checkUserName(userBO.getUserName());
        this.checkMobile(userBO.getMobile());
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userBO);
        try {
            //解密密码
            String password = this.decryptPassword(userBO.getPassword());
            //sm4密码加密
            userDO.setPassword(PasswordUtil.createPassWord(password, systemAccessTokenService.getAesKey()));
        } catch (Exception e) {
            log.error("createPassWord Exception {}", e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
        if (JudgeUtils.isEmpty(userBO.getUserId())) {
            userDO.setUserId(IdGenUtil.generatorUserId());
        }
        userDO.setCreateUserId(operatorId);
        userDO.setHasRole(hasRole);
        int insert = userExtDao.insert(userDO);
        if (insert < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
        if (JudgeUtils.isNotEmpty(roleIdList)) {
            this.deleteRoleByUserId(userDO.getUserId());
            this.batchInsertUserRole(userDO.getUserId(), roleIdList);
            this.insertLoginLatestInfo(userDO.getUserId());
        }
    }

    /**
     * 判断用户是否可以登录，此角色标记会影响用户是否能够正常登录，Y才允许登录系统
     *
     * @param operatorId
     * @param userBO
     * @param roleIdList
     * @return
     */
    public String getHasRole(String operatorId, UserBO userBO, List<Long> roleIdList) {
        return JudgeUtils.isNotEmpty(roleIdList) ? StatusConstans.HAS_ROLE.getValue() : StatusConstans.NOT_HAS_ROLE.getValue();
    }

    @Override
    public void batchAdd(UserBO userBO) {
        this.checkUserName(userBO.getUserName());
        this.checkMobile(userBO.getMobile());
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userBO);
        if (JudgeUtils.isEmpty(userBO.getUserId())) {
            userDO.setUserId(IdGenUtil.generatorUserId());
        }
        userDO.setCreateUserId("task");
        userDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        int insert = userExtDao.insert(userDO);
        if (insert < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_INSERT_FAILED);
        }
    }

    @Override
    public void batchUpdate(UserBO userBO) {
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userBO);
        userDO.setPassword(null);
        userDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        int update = userExtDao.update(userDO);
        if (update < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
    }

    @Override
    public void updateUserByMobile(UserBO userBO) {
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userBO);
        userDO.setPassword(null);
        userDO.setModifyTime(DateTimeUtils.getCurrentLocalDateTime());
        userExtDao.updateUserByMobile(userDO);
    }

    @Override
    public void checkUserName(String userName) {
        UserDO userDO = new UserDO();
        userDO.setUserName(userName);
        List<UserDO> userDOS = userExtDao.find(userDO);
        if (JudgeUtils.isNotEmpty(userDOS)) {
            BusinessException.throwBusinessException(MsgEnum.USERNAME_ALREADY_EXISTS);
        }
    }


    @Override
    public void checkMobile(String mobile) {
        UserDO userDO = new UserDO();
        userDO.setMobile(mobile);
        List<UserDO> userDOS = userExtDao.find(userDO);
        if (JudgeUtils.isNotEmpty(userDOS)) {
            BusinessException.throwBusinessException(MsgEnum.MOBILE_ALREADY_EXISTS);
        }
    }

    @Override
    public UserBO getUserInfo(UserBO userBO) {
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userBO);
        List<UserDO> userDOS = userExtDao.find(userDO);
        if (JudgeUtils.isEmpty(userDOS)) {
            return null;
        }
        BeanUtils.copyProperties(userBO, userDOS.get(0));
        return userBO;
    }

    @Override
    public String encryptPassword(String password) {

        //获取公钥
        String publicKey = systemAccessTokenService.getPublicKey(appId);
        if (JudgeUtils.isBlank(publicKey)) {
            BusinessException.throwBusinessException(MsgEnum.FAILED_TO_GET_ACCESSTOKEN);
        }
        //密码转加密
        String encryptPassword = null;
        try {
            encryptPassword = SM2EncryptorUtil.encrypt(publicKey, password);
        } catch (Exception e) {
            BusinessException.throwBusinessException(MsgEnum.LOGIN_ACCOUNT_OR_PASSWORD_ERROR);
        }
        if (JudgeUtils.isBlank(password)) {
            BusinessException.throwBusinessException(MsgEnum.LOGIN_ACCOUNT_OR_PASSWORD_ERROR);
        }
        return encryptPassword;
    }

    @Override
    public String decryptPassword(String password) {
        return SM2EncryptorUtil.decrypt(systemAccessTokenService.getPrivateKey(), password);
    }

    @Override
    public List<UserBO> getAllUserMobiles() {
        List<UserBO> users = userExtDao.getAllUserMobiles();
        if (JudgeUtils.isEmpty(users)) {
            return new ArrayList<>();
        }
        return users;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void disableBatch(List<String> userIds) {
        userIds.stream().forEach(this::disable);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void disable(String userId) {
        int disable = userExtDao.disable(userId, LocalDateTime.now());
        if (disable < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void enableBatch(List<String> userIds) {
        userIds.stream().forEach(this::enable);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void enable(String userId) {
        int enable = userExtDao.enable(userId, LocalDateTime.now());
        if (enable < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
    }

    @Override
    public void updateDateTime(UserBO userInfo) {
        UserDO userDO = new UserDO();
        userDO.setUserId(userInfo.getUserId());
        userDO.setLastLoginTime(userInfo.getLastLoginTime());
        userExtDao.update(userDO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void delete(String userId) {
        int delete = userExtDao.delete(userId);
        if (delete < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_DELETE_FAILED);
        }
        //删除用户角色
        this.deleteRoleByUserId(userId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void deleteUserByMobile(String mobile) {
        List<UserBO> users = userExtDao.queryUsersByMobile(mobile);
        if (JudgeUtils.isEmpty(users)) {
            return;
        }
        users.forEach(user -> {
            this.delete(user.getUserId());
        });
    }

    @Override
    public void deleteUserRole(String userId) {
        this.deleteRoleByUserId(userId);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public int deleteRoleByUserId(String userId) {
        return userRoleExtDao.deleteByUserId(userId);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public int batchInsertUserRole(String userId, List<Long> roleIdList) {
        return userRoleExtDao.batchInsertUserRole(userId, roleIdList);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public int insertLoginLatestInfo(String userId) {
        LoginLatestInfoDO loginLatestInfoDO = new LoginLatestInfoDO();
        loginLatestInfoDO.setId(UUID.randomUUID().toString().replace("-", "").toUpperCase());
        loginLatestInfoDO.setUserId(userId);
        loginLatestInfoDO.setPid(UUID.randomUUID().toString().replace("-", "").toUpperCase());
        loginLatestInfoDO.setLatestDate(LocalDate.now());
        loginLatestInfoDO.setLatestTime(LocalDateTime.now());
        loginLatestInfoDO.setFirstDate(LocalDate.now());
        loginLatestInfoDO.setFirstTime(LocalDateTime.now());
        loginLatestInfoDO.setIsUse((short) 1);
        return loginLatestInfoExtDao.insert(loginLatestInfoDO);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public int updateLoginLatestInfo(LoginLatestInfoDO loginLatestInfoDO) {
        loginLatestInfoDO.setLatestDate(LocalDate.now());
        loginLatestInfoDO.setLatestTime(LocalDateTime.now());
        loginLatestInfoDO.setIsUse((short) 1);
        return loginLatestInfoExtDao.update(loginLatestInfoDO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void deleteBatch(List<String> userIds) {
        userIds.stream().forEach(this::delete);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void update(String operatorId, UserBO userBO, List<Long> roleIdList) {
        String hasRole = getHasRole(operatorId, userBO, roleIdList);
        UserDO queryUserDO = userExtDao.get(userBO.getUserId());
        //不是原来用户名
        if (!JudgeUtils.equalsIgnoreCase(userBO.getUserName(), queryUserDO.getUserName())) {
            this.checkUserName(userBO.getUserName());
        }
        //不是原来手机号码
        if (!JudgeUtils.equalsIgnoreCase(userBO.getMobile(), queryUserDO.getMobile())) {
            this.checkMobile(userBO.getMobile());
        }
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userBO);
        if (JudgeUtils.isBlank(userDO.getPassword())) {
            userDO.setPassword(null);
        } else {
            try {
                //解密密码
                String password = this.decryptPassword(userBO.getPassword());
                //sm4密码加密
                userDO.setPassword(PasswordUtil.createPassWord(password, systemAccessTokenService.getAesKey()));
            } catch (Exception e) {
                log.error("createPassWord Exception {}", e.getMessage());
                BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
            }
        }
        userDO.setHasRole(hasRole);
        int update = userExtDao.update(userDO);
        if (update < 1) {
            BusinessException.throwBusinessException(MsgEnum.DB_UPDATE_FAILED);
        }
        this.deleteRoleByUserId(userDO.getUserId());
        if (JudgeUtils.isNotEmpty(roleIdList)) {
            this.batchInsertUserRole(userDO.getUserId(), roleIdList);
            //更新用户登录信息
            LoginLatestInfoDO loginLatestInfoDO = new LoginLatestInfoDO();
            loginLatestInfoDO.setUserId(userDO.getUserId());
            List<LoginLatestInfoDO> latestInfoDOS = loginLatestInfoExtDao.find(loginLatestInfoDO);
            if (JudgeUtils.isEmpty(latestInfoDOS)) {
                this.insertLoginLatestInfo(userDO.getUserId());
            } else {
                this.updateLoginLatestInfo(latestInfoDOS.get(0));
            }
        }
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = BusinessException.class)
    public void updatePassword(String userId, String oldPassword, String newPassword) {
        UserBO userBO = new UserBO();
        userBO.setUserId(userId);
        UserBO userInfo = this.getUserInfo(userBO);
        String oldPasswordFormDataBase = PasswordUtil.decryptPassword(userInfo.getPassword(), systemAccessTokenService.getAesKey());
        //旧密码是否正确
        if (!JudgeUtils.equalsIgnoreCase(oldPasswordFormDataBase, oldPassword)) {
            BusinessException.throwBusinessException(MsgEnum.WRONG_ORIGIN_PASSWORD);
        }
        //新密码是否与之前的密码相同,此处为密码明文
        if (JudgeUtils.equalsIgnoreCase(newPassword, oldPassword)) {
            BusinessException.throwBusinessException(MsgEnum.SAME_PASSWORD);
        }
        UserDO userDO = new UserDO();
        try {
            userDO.setPassword(PasswordUtil.createPassWord(newPassword, systemAccessTokenService.getAesKey()));
        } catch (Exception e) {
            log.error("createPassWord Exception {}", e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.WRONG_ORIGIN_PASSWORD);
        }
        userDO.setUserId(userId);
        userExtDao.update(userDO);
    }

    @Override
    public List<UserBO> getAllUsers(String operatorId) {
        List<UserBO> userBOS = new ArrayList<>();
        UserDO userDO = new UserDO();
        List<UserDO> userDOS = userExtDao.find(userDO);
        if (JudgeUtils.isNotEmpty(userDOS)) {
            userBOS = BeanConvertUtil.convertList(userDOS, UserBO.class);
        }
        return userBOS;
    }

    @Override
    public List<UserBO> getUsersByCondition(UserBO userBO) {
        UserDO userDO = new UserDO();
        BeanUtils.copyProperties(userDO, userBO);
        List<UserDO> userDOS = userExtDao.find(userDO);
        if (JudgeUtils.isEmpty(userDOS)) {
            return null;
        }
        return BeanConvertUtil.convertList(userDOS, UserBO.class);
    }

    @Override
    public List<UserBO> getUsersByRoleId(Long roleId) {
        List<UserBO> userBOS = new ArrayList<>();
        List<UserDO> userDOS = userRoleExtDao.getUsersByRoleId(roleId);
        if (JudgeUtils.isNotEmpty(userDOS)) {
            userBOS = BeanConvertUtil.convertList(userDOS, UserBO.class);
        }
        return userBOS;
    }


}
