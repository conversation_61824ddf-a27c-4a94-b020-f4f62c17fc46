package com.cmpay.hacp.log.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogRecord {
    /**
     * 日志标题
     * 1.优先取注解中的配置
     * 2.如果为空，则根据 @PreAuthorize注解中的值获取菜单名称，eg 系统管理/用户管理/查询
     * 3.如果以上均为空，则取请求的API地址
     */
    String title();

    /**
     * 所属应用
     * 1.优先取注解中的配置
     * 2.如果为空,则取系统日志配置lemon.web.admin.log.dataIt
     * 3.如果系统日志配置为空，则取应用名称spring.application.name
     */
    String dataIt() default "";


    /**
     * 操作动作
     * 1.优先取注解中的配置
     * 2.如果为空,则取http请求方法get,post登
     */
    String action();
}
