/*
 * @ClassName SystemLogDO
 * @Description
 * @version 1.0
 * @Date 2023-08-08 16:10:59
 */
package com.cmpay.hacp.entity;

import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import com.cmpay.lemon.framework.annotation.DataObject;

import java.time.LocalDateTime;

@DataObject
public class SystemLogDO extends BaseDO {
    /**
     * @Fields id 编号
     */
    private String id;
    /**
     * @Fields type 日志类型
     */
    private String type;
    /**
     * @Fields title 操作动作
     */
    private String title;
    /**
     * @Fields userId 操作者ID
     */
    private String userId;
    /**
     * @Fields userName 操作者
     */
    private String userName;
    /**
     * @Fields msgCd 消息码
     */
    private String msgCd;
    /**
     * @Fields mobile 操作者手机号码
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;
    /**
     * @Fields createBy 操作者ID
     */
    private String createBy;
    /**
     * @Fields createDate 执行时间
     */
    private LocalDateTime createDate;
    /**
     * @Fields remoteAddr 操作IP地址
     */
    private String remoteAddr;
    /**
     * @Fields userAgent 用户终端
     */
    private String userAgent;
    /**
     * @Fields requestUri 执行对象
     */
    private String requestUri;
    /**
     * @Fields applicationName 应用名称
     */
    private String applicationName;
    /**
     * @Fields dataIt 数据所属对象
     */
    private String dataIt;
    /**
     * @Fields method 操作方式
     */
    private String method;
    /**
     * @Fields requestId 日志号
     */
    private String requestId;
    /**
     * @Fields rspDataSize 响应报文大小
     */
    private Long rspDataSize;
    /**
     * @Fields rspDataSizeType 响应报文大小类型(line-行数、byte-大小)
     */
    private String rspDataSizeType;
    /**
     * @Fields duration 耗时(毫秒-millisecond）
     */
    private Long duration;
    /**
     * @Fields endTime 结束时间
     */
    private LocalDateTime endTime;
    /**
     * @Fields tenantId 租户ID
     */
    private String tenantId;
    /**
     * @Fields workspaceId 项目ID
     */
    private String workspaceId;
    /**
     * @Fields params 请求参数
     */
    private String params;
    /**
     * @Fields exception 异常信息
     */
    private String exception;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMsgCd() {
        return msgCd;
    }

    public void setMsgCd(String msgCd) {
        this.msgCd = msgCd;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public String getRemoteAddr() {
        return remoteAddr;
    }

    public void setRemoteAddr(String remoteAddr) {
        this.remoteAddr = remoteAddr;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public String getDataIt() {
        return dataIt;
    }

    public void setDataIt(String dataIt) {
        this.dataIt = dataIt;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Long getRspDataSize() {
        return rspDataSize;
    }

    public void setRspDataSize(Long rspDataSize) {
        this.rspDataSize = rspDataSize;
    }

    public String getRspDataSizeType() {
        return rspDataSizeType;
    }

    public void setRspDataSizeType(String rspDataSizeType) {
        this.rspDataSizeType = rspDataSizeType;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getWorkspaceId() {
        return workspaceId;
    }

    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = exception;
    }
}