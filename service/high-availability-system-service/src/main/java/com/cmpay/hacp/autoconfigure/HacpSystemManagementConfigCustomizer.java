package com.cmpay.hacp.autoconfigure;

import com.cmpay.lemonframework.autoconfigure.hazelcast.ConfigCustomizer;
import com.hazelcast.config.Config;
import com.hazelcast.config.MapConfig;

import static com.cmpay.hacp.constant.TenantConstant.WORKSPACE_APPLICATION_JOB_LOCK;

/**
 * <AUTHOR>
 */
public class HacpSystemManagementConfigCustomizer implements ConfigCustomizer {

    @Override
    public void customize(Config config) {
        config.addMapConfig(this.systemManagementMapConfig());
    }

    public MapConfig systemManagementMapConfig() {
        MapConfig systemManagementMapConfig = new MapConfig();
        systemManagementMapConfig.setTimeToLiveSeconds(60 * 10);
        systemManagementMapConfig.setName(WORKSPACE_APPLICATION_JOB_LOCK);
        return systemManagementMapConfig;
    }
}
