/*
 * @ClassName IRiseSystemUserExtDao
 * @Description
 * @version 1.0
 * @Date 2023-08-02 16:20:01
 */
package com.cmpay.hacp.dao;

import com.cmpay.hacp.bo.system.SystemLogBO;
import com.cmpay.hacp.log.bo.SysDynamicLogBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ISystemLogExtDao extends ISystemLogDao {
    /**
     * 查询系统日志列表
     *
     * @param riseSystemLog 日志详情
     * @return 系统日志列表
     */
    List<SystemLogBO> getSystemLogs(SystemLogBO riseSystemLog);

    /**
     * 删除日志
     *
     * @param ids
     * @return
     */
    int deleteByIds(@Param("ids") List<String> ids);

    /**
     * 查询日志详情
     *
     * @param id 日志id
     * @return 日志详情
     */
    SystemLogBO getSystemLogInfo(@Param("id") String id);

    /**
     * 查询系统日志列表
     *
     * @param requestId 日志详情
     * @return 系统日志列表
     */
    List<SysDynamicLogBO> getDynamicLogs(@Param("requestId") String requestId);
}
