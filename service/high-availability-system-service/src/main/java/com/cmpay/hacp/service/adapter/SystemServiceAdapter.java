package com.cmpay.hacp.service.adapter;

import com.cmpay.hacp.bo.menu.MenuTreeBO;
import com.cmpay.hacp.bo.system.SystemUserBO;
import com.cmpay.hacp.dao.IUserExtDao;
import com.cmpay.hacp.entity.MenuDO;
import com.cmpay.hacp.service.SystemService;
import com.cmpay.hacp.utils.MenuUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 系统管理
 */
@Service
public class SystemServiceAdapter implements SystemService {
    private final static String USER_STATUS = "ENABLE";

    @Resource
    private IUserExtDao userExtDao;

    @Resource
    private MenuUtils menuUtils;

    @Override
    public List<SystemUserBO> getSystemUsers() {
        SystemUserBO user = new SystemUserBO();
        user.setStatus(USER_STATUS);
        return userExtDao.getSystemUsers(user);
    }


    @Override
    public List<MenuTreeBO> getSystemTreeMenus() {
        List<MenuDO> systemMenus = this.getSystemMenus();
        if (JudgeUtils.isNotEmpty(systemMenus)) {
            return menuUtils.getTreeMenus(systemMenus);
        }
        return new ArrayList<>();
    }

    @Override
    public List<MenuDO> getSystemMenus() {
        return null;
    }
}
