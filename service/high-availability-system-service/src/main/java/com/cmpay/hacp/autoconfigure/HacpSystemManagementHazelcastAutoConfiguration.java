package com.cmpay.hacp.autoconfigure;

import com.cmpay.lemonframework.autoconfigure.hazelcast.ConfigCustomizer;
import com.cmpay.lemonframework.autoconfigure.hazelcast.HazelcastServerAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AutoConfigureBefore({HazelcastServerAutoConfiguration.class})
public class HacpSystemManagementHazelcastAutoConfiguration {
    @Bean
    public ConfigCustomizer hacpSystemManagementConfigCustomizer() {
        return new HacpSystemManagementConfigCustomizer();
    }
}
