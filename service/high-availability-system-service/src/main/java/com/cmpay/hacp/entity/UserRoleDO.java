/*
 * @ClassName UserRoleDO
 * @Description
 * @version 1.0
 * @Date 2021-09-02 15:24:38
 */
package com.cmpay.hacp.entity;

import com.cmpay.lemon.framework.annotation.DataObject;

@DataObject
public class UserRoleDO extends BaseDO {
    /**
     * @Fields id
     */
    private String id;
    /**
     * @Fields userId 用户id
     */
    private String userId;
    /**
     * @Fields roleId 角色id
     */
    private Long roleId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
}