/*
 * @ClassName DictDO
 * @Description
 * @version 1.0
 * @Date 2020-04-21 09:38:45
 */
package com.cmpay.hacp.bo.system;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DictBO {
    /**
     * @Fields dictId 编号
     */
    private String dictId;
    /**
     * @Fields value 数据值
     */
    private String value;
    /**
     * @Fields label 标签名
     */
    private String label;
    /**
     * @Fields type 类型
     */
    private String type;
    /**
     * @Fields description 描述
     */
    private String description;
    /**
     * @Fields sort 排序（升序）
     */
    private Long sort;
    /**
     * @Fields parentId 父级编号
     */
    private String parentId;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;

    private List<DictBO> child;

}
