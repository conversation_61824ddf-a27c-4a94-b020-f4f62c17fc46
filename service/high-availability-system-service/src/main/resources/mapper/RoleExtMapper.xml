<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IRoleExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.RoleDO">
        <id column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="owner_app_id" property="ownerAppId" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="BaseResultExtMap" type="com.cmpay.hacp.bo.system.RoleBO">
        <id column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="owner_app_id" property="ownerAppId" jdbcType="VARCHAR"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        role_id,
        role_name,
        remark,
        status,
        dept_id,
        create_user_id,
        create_time,
        modify_time,
        owner_app_id
    </sql>
    <select id="getRoles" resultMap="BaseResultExtMap">
        select r.role_id,
        r.role_name,
        r.remark,
        r.status,
        r.dept_id,
        r.create_user_id,
        r.create_time,
        r.modify_time,
        r.owner_app_id,
        d.dept_name
        from sys_role as r,
        sys_dept as d
        where r.dept_id = d.dept_id
        <if test="roleDO.roleId != null">
            and r.role_id = #{roleDO.roleId,jdbcType=BIGINT}
        </if>
        <if test="roleDO.roleName != null and roleDO.roleName != ''">
            and r.role_name like concat('%',#{roleDO.roleName,jdbcType=VARCHAR},'%')
        </if>
        <if test="roleDO.status != null and roleDO.status != ''">
            and r.status = #{roleDO.status,jdbcType=VARCHAR}
        </if>
        <if test="roleDO.deptId != null and roleDO.deptId != ''">
            and r.dept_id = #{roleDO.deptId,jdbcType=VARCHAR}
        </if>
        <if test="roleDO.ownerAppId != null and roleDO.ownerAppId != ''">
            and r.owner_app_id = #{roleDO.ownerAppId,jdbcType=VARCHAR}
        </if>
        <if test="deptIds != null and deptIds.size() != 0">
            and r.dept_id in
            <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                #{deptId,jdbcType=VARCHAR}
            </foreach>
        </if>
        order by r.modify_time desc
    </select>

    <insert id="insertKey" useGeneratedKeys="true" keyProperty="roleId"
            parameterType="com.cmpay.hacp.entity.RoleDO">
        insert into sys_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="roleName != null">
                role_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="ownerAppId != null">
                owner_app_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="roleName != null">
                #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null and deptId != ''">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="ownerAppId != null">
                #{ownerAppId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="findRoleId" parameterType="java.lang.String" resultType="java.math.BigInteger">
        select
        role_id
        from sys_role
        <where>
            <if test="roleName != null and roleName != ''">
                and role_name = #{roleName,jdbcType=VARCHAR}
            </if>
            <if test="deptId != null and deptId != ''">
                and dept_id = #{deptId,jdbcType=VARCHAR}
            </if>
        </where>
        order by create_time asc
    </select>
</mapper>
