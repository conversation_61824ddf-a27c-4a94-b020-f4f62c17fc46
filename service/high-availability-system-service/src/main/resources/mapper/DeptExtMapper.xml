<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cmpay.hacp.dao.IDeptExtDao">
    <resultMap id="BaseResultMap" type="com.cmpay.hacp.entity.DeptDO">
        <id column="dept_id" property="deptId" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        dept_id,
        parent_id,
        dept_name,
        order_num,
        status,
        create_user_id,
        create_time,
        modify_time
    </sql>
    <select id="queryDepartments" parameterType="com.cmpay.hacp.entity.DeptDO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        <where>
            <if test="deptDO.deptId != null and deptDO.deptId != ''">
                and dept_id = #{deptDO.deptId,jdbcType=VARCHAR}
            </if>
            <if test="deptDO.parentId != null">
                and parent_id = #{deptDO.parentId,jdbcType=BIGINT}
            </if>
            <if test="deptDO.deptName != null and deptDO.deptName != ''">
                and dept_name = #{deptDO.deptName,jdbcType=VARCHAR}
            </if>
            <if test="deptDO.status != null  and deptDO.status != ''">
                and status = #{deptDO.status,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryChildDepartments" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        <where>
            <if test="deptDO.deptId != null and deptDO.deptId != ''">
                and dept_id = #{deptDO.deptId,jdbcType=VARCHAR}
            </if>
            <if test="deptDO.parentId != null">
                and parent_id = #{deptDO.parentId,jdbcType=BIGINT}
            </if>
            <if test="deptDO.deptName != null and deptDO.deptName != ''">
                and dept_name = #{deptDO.deptName,jdbcType=VARCHAR}
            </if>
            <if test="deptDO.status != null  and deptDO.status != ''">
                and status = #{deptDO.status,jdbcType=VARCHAR}
            </if>
            <if test="deptIds != null and deptIds.size() != 0">
                and dept_id in
                <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                    #{deptId,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getAllDeptIds" resultType="java.lang.String">
        select dept_id
        from sys_dept
    </select>
</mapper>
