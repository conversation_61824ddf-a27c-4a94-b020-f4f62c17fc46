package com.cmpay.hacp.dispatch.bo;

import com.cmpay.hacp.capable.TenantCapable;
import com.cmpay.hacp.dispatch.entity.AppInstanceMachineDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppInstanceMachineBO extends AppInstanceMachineDO implements TenantCapable {


    private String instanceGroupName;
    private String instanceGroupCn;

    private String instanceGroupDesc;

    private String externalRouterAddress;
    /**
     * @Fields checkInterval 检查间隔(毫秒)
     */
    private Short checkInterval;
    /**
     * @Fields checkFall 连续失败次数
     */
    private Short checkFall;
    /**
     * @Fields checkRise 连续成功次数
     */
    private Short checkRise;
    /**
     * @Fields checkTimeout 检查超时时间(毫秒)
     */
    private Short checkTimeout;
    /**
     * @Fields checkPort 检查端口
     */
    private Short checkPort;
    /**
     * @Fields checkHttpSend Http检查报文
     */
    private String checkHttpSend;
    /**
     * @Fields checkHttpExpectAlive Http检查预期状态码
     */
    private String checkHttpExpectAlive;

    private String zoneIds;

    private String zoneLabel;

    private String addresses;

    private List<Zone> zoneList;

    public void convertToZone(){
        if(StringUtils.isNotBlank(this.getZoneIds())) {
            String[] zoneIds = this.getZoneIds().split(",");
            String[] addresses = this.getAddresses().split(",");
            Map<String, Zone> zoneMap = new HashMap<>();
            for (int i = 0; i < zoneIds.length; i++) {
                Zone zone = zoneMap.getOrDefault(zoneIds[i], new Zone(zoneIds[i], new ArrayList<>()));
                zone.getAddresses().add(addresses[i]);
                zoneMap.put(zoneIds[i], zone);
            }
            this.setZoneList(new ArrayList<>(zoneMap.values()));
            this.setZoneIds(null);
            this.setAddresses(null);
        }
    }

    @Data
    public static class Zone {
        private String zoneId;
        private List<String> addresses;

        public Zone(String zoneId, List<String> addresses){
            this.zoneId = zoneId;
            this.addresses = addresses;
        }
    }

}
