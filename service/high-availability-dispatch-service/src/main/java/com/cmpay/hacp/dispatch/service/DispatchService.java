package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.DispatchBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;

public interface DispatchService{

    void addDispatch(DispatchBO dispatchBO);
    void deleteDispatch(DispatchBO dispatchBO);
    void updateDispatch(DispatchBO dispatchBO);
    PageInfo<DispatchBO> findDispatchList(int pageNum, int pageSize, DispatchBO dispatchBO);

    PageInfo<DispatchBO> getDispatchList(int pageNum, int pageSize, DispatchBO dispatchBO);

    List<DispatchBO> getDispatchList(String workspaceId);

    DispatchBO getInfo(Integer dispatchId, String workspaceId);

    void disable(DispatchBO dispatchBO);

    void enable(DispatchBO dispatchBO);

    boolean existBind(DispatchBO dispatchBO);
}