package com.cmpay.hacp.dispatch.service.camunda;

import com.cmpay.hacp.bo.task.DispatchParamBO;
import com.cmpay.hacp.bo.task.HacpEmergencyTaskBO;
import com.cmpay.hacp.bo.task.TaskParam;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.factory.AbstractTaskStrategyFactory;
import com.cmpay.hacp.service.factory.TaskStrategyType;
import com.cmpay.hacp.utils.CamundaUtil;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * Cmid相关
 *
 * <AUTHOR>
 * @since 2024-05-05 15:20:22
 */
@Slf4j
@Service(value = SystemDispatchImpl.BEAN_NAME)
public class SystemDispatchImpl extends AbstractTaskStrategyFactory {

    protected final static String BEAN_NAME = "systemDispatchImpl";

    public SystemDispatchImpl() {
        super(Collections.singletonList(new TaskStrategyType("0", "系统应急调度", BEAN_NAME)));
    }

    @Override
    public <T extends BaseElement> void bpmnTaskFieldHandle(T node, HacpEmergencyTaskBO taskInfo, BpmnModelInstance modelInstance) {
        //检查任务执行参数是否正确
        if (!checkTaskParam(taskInfo.getTaskParam())) {
            log.error("task param is error :{}", taskInfo.getTaskParam());
            BusinessException.throwBusinessException(MsgEnum.TASK_PARAM_IS_ERROR);
        }
        //设置设置服务任务执行调度
        if (node instanceof ServiceTask) {
            ((ServiceTask)node).setCamundaDelegateExpression("${doDispatch}");
            ((ServiceTask)node).setName(taskInfo.getTaskName());
        }
    }

    @Override
    public boolean isNeedEncrypt() {
        return false;
    }

    @Override
    public String getBpmTaskType() {
        return CamundaUtil.CAMUNDA_SERVICE_TASK_ELEMENT;
    }

    @Override
    public String getActivityType() {
        return CamundaUtil.SERVICE_TASK_ELEMENT;
    }

    @Override
    public boolean checkTaskParam(String json) {
        DispatchParamBO dispatchParamBO = (DispatchParamBO)toTaskParam(json);
        if (dispatchParamBO == null) {
            return false;
        }
        return !JudgeUtils.isEmpty(dispatchParamBO.getDispatchId()) && !JudgeUtils.isBlank(dispatchParamBO.getDispatchStatus());
    }

    @Override
    public TaskParam toTaskParam(String json) {
        return JsonUtil.strToObject(json, DispatchParamBO.class);
    }


}
