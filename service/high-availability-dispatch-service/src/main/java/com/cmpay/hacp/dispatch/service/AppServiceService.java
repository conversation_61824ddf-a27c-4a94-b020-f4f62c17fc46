package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.AppServiceBO;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;


public interface AppServiceService {

    void addAppService(AppServiceBO appServiceDO);
    void deleteAppService(AppServiceBO appServiceDO);
    void updateAppService(AppServiceBO appServiceDO);
    PageInfo<AppServiceBO> getAppServicePage(int pageNum, int pageSize, AppServiceBO appServiceBO);

    List<AppServiceBO> getAppServiceList(String workspaceId);

    AppServiceBO getAppService(Integer appServiceId);
}
