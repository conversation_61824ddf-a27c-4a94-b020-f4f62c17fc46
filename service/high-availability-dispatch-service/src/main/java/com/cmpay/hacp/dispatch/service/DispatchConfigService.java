package com.cmpay.hacp.dispatch.service;

import com.cmpay.hacp.dispatch.bo.GenerateDispatchConfigBO;
import com.cmpay.hacp.dispatch.bo.*;
import com.cmpay.lemon.framework.page.PageInfo;

import java.util.List;
import java.util.Set;

public interface DispatchConfigService {

    PageInfo<DispatchConfigBO> getDispatchConfigList(int pageNum, int pageSize, DispatchConfigBO dispatchConfigBO);

    /**
     * 不传版本号, 默认返回生产版本
     *
     * @param dispatchConfigBO
     * @return
     */
    DispatchConfigBO getDispatchConfig(DispatchConfigBO dispatchConfigBO);
    DispatchConfigBO getDispatchGraph(DispatchConfigBO dispatchConfigBO);

    /**
     * 预先生产发布版本
     * @param preConfig
     * @return
     */
    DispatchConfigBO preGenerateDispatchConfig(GenerateDispatchConfigBO preConfig);

    DispatchConfigBO publish(DispatchConfigBO dispatchConfigBO);
    DispatchConfigBO publishAndPush(DispatchConfigBO dispatchConfigBO);

    List<ApiLocationBO> getApilocationFromDispatchConfig(DispatchConfigBO dispatchConfigBO);
    PageInfo<DispatchBO> getDispatchFromDispatchConfigByApilocation(DispatchConfigBO dispatchConfigBO, ApiLocationBO apiLocationBO);
    PageInfo<DispatchNodeBO> getPushHistoryByDispatchConfig(DispatchConfigBO dispatchConfigBO);

    /**
     * 批量关闭应急调度
     * @param preConfig
     * @param closeEmergencyDispatches
     * @return
     */
    boolean closeDispatches(GenerateDispatchConfigBO preConfig, Set<String> closeEmergencyDispatches);
    /**
     * 批量开启应急调度
     * @param preConfig
     * @param openEmergencyDispatches
     * @return
     */
    boolean openDispatches(GenerateDispatchConfigBO preConfig, Set<String> openEmergencyDispatches);

    boolean openOrCloseDispatches(GenerateDispatchConfigBO preConfig, Set<String> openEmergencyDispatches, Set<String> closeEmergencyDispatches);

        void disableDispatchConfig(DispatchConfigBO dispatchConfigBO);
    void deleteDispatchConfig(DispatchConfigBO dispatchConfigBO);

    DispatchConfigBO getPushDataDropDown(String workspaceId);
}