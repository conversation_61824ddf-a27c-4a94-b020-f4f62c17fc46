package com.cmpay.hacp.container.service.impl;

import com.alibaba.fastjson2.JSON;
import com.cmpay.hacp.bo.KubesphereMetaData;
import com.cmpay.hacp.constant.CommonConstant;
import com.cmpay.hacp.container.bo.EmergencyContainerBO;
import com.cmpay.hacp.container.client.KubesphereClient;
import com.cmpay.hacp.container.client.dto.*;
import com.cmpay.hacp.container.service.EmergencyContainerService;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.service.SystemCacheService;
import com.cmpay.hacp.service.SystemCipherService;
import com.cmpay.hacp.utils.JsonUtil;
import com.cmpay.hacp.utils.TenantUtils;
import com.cmpay.lemon.common.exception.BusinessException;
import com.cmpay.lemon.common.utils.JudgeUtils;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/14 15:21
 */
@Component
public class HacpKubesphereClient {
    private static final Logger logger = LoggerFactory.getLogger(HacpKubesphereClient.class);

    private final EmergencyContainerService containerService;
    private final SystemCipherService systemCipherService;

    private final SystemCacheService systemCacheService;

    private final KubesphereClient kubesphereClient;

    public HacpKubesphereClient(EmergencyContainerService containerService,
            SystemCipherService systemCipherService,
            SystemCacheService systemCacheService,
            KubesphereClient kubesphereClient) {
        this.containerService = containerService;
        this.systemCipherService = systemCipherService;
        this.systemCacheService = systemCacheService;
        this.kubesphereClient = kubesphereClient;
    }

    private KubesphereTokenReqDTO buildTokenBody(EmergencyContainerBO bo, String workspaceId) {
        KubesphereTokenReqDTO tokenBody = new KubesphereTokenReqDTO();
        EmergencyContainerBO tenantContainerBO = new EmergencyContainerBO();
        tenantContainerBO.setWorkspaceId(workspaceId);
        tenantContainerBO.setClientId(bo.getClientId());
        tenantContainerBO.setContainerId(bo.getContainerId());
        tenantContainerBO.setClientSecret(bo.getClientSecret());
        tenantContainerBO.setUsername(bo.getUsername());
        EmergencyContainerBO detailInfo = containerService.getDecryptDetailInfo(tenantContainerBO);
        if (CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())&&JudgeUtils.isNull(detailInfo)){
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_PASSWORD_IS_ERROR);
        }
        if (JudgeUtils.isNull(detailInfo)) {
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_CONFIG_IS_NULL);
        }
        tokenBody.setClient_id(detailInfo.getClientId());
        tokenBody.setClient_secret(detailInfo.getClientSecret());
        tokenBody.setGrant_type(detailInfo.getGrantType());
        tokenBody.setUsername(detailInfo.getUsername());
        tokenBody.setPassword(detailInfo.getPassword());
        tokenBody.setWorkspaceId(detailInfo.getCloudWorkspaceId());
        tokenBody.setClusters(JSON.parseArray(detailInfo.getCloudClusters(), String.class));
        return tokenBody;
    }

    public List<KubesphereMetaData> queryWorkspaces(EmergencyContainerBO bo) {
        KubesphereTokenReqDTO tokenBody = new KubesphereTokenReqDTO();
        if (JudgeUtils.isNotNull(bo.getContainerId()) && CommonConstant.ENCRYPTED_DISPLAY.equals(bo.getPassword())) {
            tokenBody = buildTokenBody(bo, TenantUtils.getWorkspaceIdNotNull());
        } else {
            if (JudgeUtils.isNotBlank(bo.getPassword())) {
                bo.setPassword(systemCipherService.encryptPassword(bo.getUuid(), bo.getPassword()));
            }
            tokenBody.setClient_id(bo.getClientId());
            tokenBody.setClient_secret(bo.getClientSecret());
            tokenBody.setGrant_type(bo.getGrantType());
            tokenBody.setUsername(bo.getUsername());
            tokenBody.setPassword(bo.getPassword());
            tokenBody.setWorkspaceId(bo.getCloudWorkspaceId());
        }
        //先获取token
        KubesphereTokenRspDTO token = getToken(tokenBody);
        if (token != null) {
            KubesphereRspDTO workspaces = kubesphereClient.getWorkspaces(token.getAccessToken());
            if (JudgeUtils.isNull(workspaces)) {
                return null;
            }
            return parseMetaDataWorkSpace(workspaces);
        }
        return null;
    }

    public List<KubesphereMetaData> queryClusters(String workspaceId) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        if (token != null) {
            KubesphereClusterRspDTO clusters = kubesphereClient.getClusters(token.getTokenReqDTO().getWorkspaceId(), token.getAccessToken());
            if (JudgeUtils.isNull(clusters)) {
                return null;
            }
            return parseSingleWorkSpace(clusters, token.getTokenReqDTO().getClusters());
        }
        return null;
    }

    public List<KubesphereMetaData> queryNameSpaces(String workspaceId, String cluster) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        if (token != null) {
            KubesphereRspDTO body = kubesphereClient.getNs(cluster, token.getAccessToken());
            if (JudgeUtils.isNull(body)) {
                return null;
            }
            return parse(body);
        }
        return null;
    }

    public KubesphereTokenRspDTO getToken(String workspaceId) {
        //String workspaceId = TenantUtils.getWorkspaceIdNotNull();
        KubesphereTokenReqDTO tokenParam = buildTokenBody(new EmergencyContainerBO(), workspaceId);
        KubesphereTokenRspDTO token = getToken(tokenParam);
        token.setTokenReqDTO(tokenParam);
        return token;
    }

    public KubesphereTokenRspDTO getToken(KubesphereTokenReqDTO tokenParam) {
        String key = tokenParam.getClient_id() + "-" + tokenParam.getUsername();
        if (systemCacheService.exists(key)) {
            return JSON.parseObject(systemCacheService.getValue(key).toString(), KubesphereTokenRspDTO.class);
        }
        try {
            KubesphereTokenRspDTO token = kubesphereClient.getToken(tokenParam);
            token.setAccessToken("Bearer " + token.getAccessToken());
            if (JudgeUtils.isBlank(tokenParam.getWorkspaceId())) {
                return token;
            }
            token.setWorkspaceId(tokenParam.getWorkspaceId());
            systemCacheService.setValue(key, JsonUtil.objToStr(token), 6000, TimeUnit.SECONDS);
            return token;
        } catch (Exception e) {
            // 记录错误日志或抛出自定义异常
            logger.info("Failed to get token: " + e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.KUBESPHERE_PERMISSION_IS_ERROR);
        }
        return null;
    }

    public Map<String, List<String>> queryPods(String workspaceId, String cluster, String namespace) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        if (token != null) {
            KubesphereNsPodsDTO nsPods = kubesphereClient.getNsPods(cluster, namespace, token.getAccessToken());
            return parsePods(nsPods);
        }
        return null;
    }

    public ResponseStatusRspDTO deletePod(String workspaceId, String cluster, String namespace, String pod) {
        //先获取token
        KubesphereTokenRspDTO token = getToken(workspaceId);
        ResponseStatusRspDTO responseStatusRspDTO = new ResponseStatusRspDTO();
        try {
            String response= kubesphereClient.deletePod(cluster, namespace, pod, token.getAccessToken());
            responseStatusRspDTO.setStatus(200);
            responseStatusRspDTO.setMessage(response);
        } catch (FeignException e) {
            responseStatusRspDTO.setStatus(e.status());
            responseStatusRspDTO.setMessage(e.getMessage());
            BusinessException.throwBusinessException(MsgEnum.POD_NOT_EXIST);
        }
        return responseStatusRspDTO;
    }

    private List<KubesphereMetaData> parseMetaDataWorkSpace(KubesphereRspDTO body) {
        Integer totalItems = body.getTotalItems();
        if (totalItems < 1) {
            return null;
        }
        ArrayList<KubesphereMetaData> mataData = new ArrayList<>();
        body.getItems()
                .forEach(f -> {
                    KubesphereMetaData y = new KubesphereMetaData();
                    List<OwnerReference> ownerReferences = f.getMetadata().getOwnerReferences();
                    String kind = Optional.ofNullable(ownerReferences).orElse(new ArrayList<>()).stream()
                            .map(OwnerReference::getKind)
                            .distinct()
                            .filter(JudgeUtils::isNotEmpty)
                            .findFirst()
                            .orElse(null);
                    String name = f.getMetadata().getName();
                    y.setType(kind);
                    y.setName(name);
                    List<Cluster> clusters = f.getSpec().getPlacement().getClusters();
                    ArrayList<KubesphereMetaData> clusterList = new ArrayList<>();
                    clusters.forEach(c->{
                        KubesphereMetaData metaData = new KubesphereMetaData();
                        metaData.setName(c.getName());
                        metaData.setType("cluster");
                        clusterList.add(metaData);
                    });
                    y.setItems(clusterList);
                    mataData.add(y);
                });
        return mataData;
    }

    private List<KubesphereMetaData> parseSingleWorkSpace(KubesphereClusterRspDTO body, List<String> limitCluster) {
        logger.info("limitCluster is {}",limitCluster);
        String name = body.getMetadata().getName();
        String kind = body.getKind();
        List<Cluster> clusters = body.getSpec().getPlacement().getClusters();
        ArrayList<KubesphereMetaData> mataData = new ArrayList<>();
        KubesphereMetaData workspace = new KubesphereMetaData();
        ArrayList<KubesphereMetaData> clusterList = new ArrayList<>();
        if(JudgeUtils.isEmpty(clusters)){
            logger.error("clusters is {}",clusters);
            return mataData;
        }
        clusters.stream().filter(f->limitCluster.contains(f.getName())).forEach(f->{
            KubesphereMetaData metaData = new KubesphereMetaData();
            metaData.setName(f.getName());
            metaData.setType("cluster");
            clusterList.add(metaData);
        });

        workspace.setType(kind);
        workspace.setName(name);
        workspace.setItems(clusterList);
        mataData.add(workspace);

        return mataData;
    }

    private Map<String, List<String>> parsePods(KubesphereNsPodsDTO body) {
        List<Item> item = body.getItems();
        HashMap<String, List<String>> hashMap = new HashMap<>();
        if (JudgeUtils.isEmpty(item)) {
            return hashMap;
        }
        item.forEach(f -> {
            KubesphereMetadata metadata = f.getMetadata();
            String name = metadata.getName();
            Map<String, String> labels = metadata.getLabels();
            if(JudgeUtils.isNull(labels)){
                return;
            }
            String key = labels.getOrDefault("app.kubernetes.io/name", null);
            if (JudgeUtils.isBlank(key)) {
                return;
            }
            if (hashMap.containsKey(key)) {
                List<String> pods = hashMap.get(key);
                pods.add(name);
            } else {
                ArrayList<String> pods = new ArrayList<>();
                pods.add(name);
                hashMap.put(key, pods);
            }
        });
        return hashMap;
    }

    private List<KubesphereMetaData> parse(KubesphereRspDTO body) {
        ArrayList<KubesphereMetaData> mataData = new ArrayList<>();
        Optional.ofNullable(body.getItems())
                .orElse(new ArrayList<>())
                .forEach(f -> {
                    KubesphereMetaData metaData = new KubesphereMetaData();
                    metaData.setType("namespace");
                    metaData.setName(f.getMetadata().getName());
                    mataData.add(metaData);
                });
        return mataData;
    }
}
