package com.cmpay.hacp.controller.extend.ticket.dto;

import com.cmpay.hacp.bo.system.SessionTokenVO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @data 2020/2/18 15:10
 * 权限认证登陆返回DTO
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class LoginRspDTO extends DefaultRspDTO<NoBody> {

    @ApiModelProperty(value = "会话Token")
    private SessionTokenVO sessionTokenVO;

    @ApiModelProperty(value = "上次登陆时间")
    private String lastLoginTime;

    @ApiModelProperty(value = "是否需要提醒用户修改密码")
    private String pwdNeedToModify;

}
