package com.cmpay.hacp.utils;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.common.exception.BusinessException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class CheckUtils {
    private static final String IP_DOMAIN_PORT_REGEX =
                    "(?:(?:[0-9]{1,3}\\.){3}[0-9]{1,3}" +  // Match IP address
                    "|(?:[a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+)" + // Match domain name including localhost
                    "(?::\\d{1,5})?$";

    private static final String PROTOCOL_IP_DOMAIN_PORT_REGEX = "^(?:https?://)?" + IP_DOMAIN_PORT_REGEX;

    private static final Pattern IP_DOMAIN_PORT_PATTERN = Pattern.compile("^" + IP_DOMAIN_PORT_REGEX);

    private static final Pattern PROTOCOL_IP_DOMAIN_PORT_PATTERN = Pattern.compile(PROTOCOL_IP_DOMAIN_PORT_REGEX);

    public static void testMain(String[] args) {
        String[] testCases = {
                "***********:8080",
                "http://***********:8080",
                "***********",
                "***************:65535",
                "example.com:80",
                "https://example.com:80",
                "example.com",
                "localhost:3000",
                "localhost",
                "tcp://localhost",
                "invalid_domain:1234",
                "256.256.256.256:1234"
        };

        for (String testCase : testCases) {
            Matcher matcher = PROTOCOL_IP_DOMAIN_PORT_PATTERN.matcher(testCase);
            System.out.println(testCase + " is " + (matcher.matches() ? "valid" : "invalid"));
        }
        System.out.println("======================");
        for (String testCase : testCases) {
            Matcher matcher = IP_DOMAIN_PORT_PATTERN.matcher(testCase);
            System.out.println(testCase + " is " + (matcher.matches() ? "valid" : "invalid"));
        }
    }

    public static boolean isInValidIpDomainPort(String ipPort){
        if(!IP_DOMAIN_PORT_PATTERN.matcher(ipPort).matches()){
            BusinessException.throwBusinessException(MsgEnum.ADDRESS_PORT_IS_ERROR);
        }
        return true;
    }
}
