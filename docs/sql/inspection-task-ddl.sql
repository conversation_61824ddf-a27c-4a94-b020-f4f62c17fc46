-- CREATE TABLE `表名` (
--     -- 主键
--                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
--
--     -- 多租户支持
--                         `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',
--
--     -- 业务字段（根据实际需求添加）
--                         `name` VARCHAR(100) NOT NULL COMMENT '名称',
--                         `description` varchar(500) DEFAULT NULL COMMENT '描述',
--
--     -- 审计字段
--                         `created_by` varchar(64) NOT NULL COMMENT '创建人ID',
--                         `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
--                         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--                         `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人ID',
--                         `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
--                         `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--
--     -- 索引
--                         PRIMARY KEY (`id`),
--                         INDEX `idx_workspace_id` (`workspace_id`),
--     -- 根据业务需求添加其他索引
--
--     -- 表备注
-- ) ENGINE=InnoDB CHARSET=utf8 COMMENT='表描述';

CREATE TABLE `inspection_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
  `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `name` varchar(128) NOT NULL COMMENT '任务名称',
  `status` tinyint(1) DEFAULT '0' COMMENT '任务状态(0禁用，1启用)',
  `description` varchar(500) DEFAULT NULL COMMENT '插件描述',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
  `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workspace_task` (`workspace_id`, `task_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='巡检任务定义表';

CREATE TABLE `inspection_task_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `rule_id` varchar(64) NOT NULL COMMENT '规则ID',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workspace_task_rule` (`workspace_id`, `task_id`, `rule_id`),
) ENGINE=InnoDB CHARSET=utf8 COMMENT='任务规则关联表';

CREATE TABLE `inspection_task_rule_resource` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `rule_id` varchar(64) NOT NULL COMMENT '规则ID',
  `resource_id` varchar(64) NOT NULL COMMENT '资源ID',
  `created_by` varchar(64) NOT NULL COMMENT '创建人',
  `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workspace_task_rule_resource` (`workspace_id`, `task_id`,`rule_id`, `resource_id`),
) ENGINE=InnoDB CHARSET=utf8 COMMENT='规则资源关联表';

CREATE TABLE `inspection_task_schedule` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',
    `task_id` varchar(64) NOT NULL COMMENT '关联的巡检任务ID',
    `schedule_type` varchar(20) NOT NULL COMMENT '调度类型(CRON/INTERVAL/FIXED_TIME)',
    `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-禁用, 1-启用)',

    -- CRON类型专用字段
    `cron_expression` varchar(100) DEFAULT NULL COMMENT 'CRON表达式',

    -- INTERVAL类型专用字段
    `interval_value` int DEFAULT NULL COMMENT '间隔值',
    `interval_unit` varchar(20) DEFAULT NULL COMMENT '间隔单位(MINUTE/HOUR/DAY/WEEK/MONTH)',

    -- FIXED_TIME类型专用字段
    `execution_date` date DEFAULT NULL COMMENT '执行日期',
    `execution_time` time DEFAULT NULL COMMENT '执行时间',

    -- 审计字段
    `created_by` varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(64) DEFAULT NULL COMMENT '修改人',
    `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',

    -- 索引
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_workspace_task_schedule` (`workspace_id`, `task_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='巡检任务调度配置表';

CREATE TABLE `inspection_task_execution_status` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，唯一标识',
  `workspace_id` varchar(64) NOT NULL COMMENT 'workspace ID',
  `task_id` bigint NOT NULL COMMENT '任务ID，外键，关联InspectionTask表的id',
  `task_name` varchar(128) NOT NULL COMMENT '任务名称，冗余存储，便于查询显示',
  `trigger_mode` varchar(16) NOT NULL DEFAULT '0' COMMENT '触发方式 0:定时触发 1:手动触发',
  `execution_status` varchar(16) NOT NULL DEFAULT '0' COMMENT '执行状态 0:待执行 1:执行中 2:已完成 3:失败 4:已停止',
  `execution_result` varchar(16) DEFAULT NULL COMMENT '执行结果 pass:通过 fail:失败',
  `scheduled_time` datetime DEFAULT NULL COMMENT '计划执行时间，定时触发时的计划时间',
  `execution_time` datetime DEFAULT NULL COMMENT '实际执行时间，任务开始执行的时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间，实际开始执行的时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间，执行完成的时间',
  `duration` int DEFAULT '0' COMMENT '耗时(秒)，执行总耗时',
  `rule_total` int DEFAULT '0' COMMENT '关联规则总数，本次执行涉及的规则总数',
  `rule_success_count` int DEFAULT '0' COMMENT '执行成功规则数',
  `rule_fail_count` int DEFAULT '0' COMMENT '执行失败规则数',
  `coverage_rate` int DEFAULT '0' COMMENT '资源覆盖率(%)',
  `success_rate` int DEFAULT '0' COMMENT '规则通过率(%)，成功规则数/总规则数',
  `result_summary` varchar(1024) DEFAULT NULL COMMENT '执行结果概要',
  `status_summary` varchar(1024) DEFAULT NULL COMMENT '状态概要',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人，创建或触发任务的用户',
  `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_execution_result` (`execution_result`),
  KEY `idx_created_time` (`created_time`),
  KEY `idx_workspace_id` (`workspace_id`)
) ENGINE=InnoDB CHARSET=utf8 COMMENT='巡检任务执行状态表';
