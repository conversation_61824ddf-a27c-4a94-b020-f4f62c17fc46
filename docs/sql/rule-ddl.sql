CREATE TABLE `inspection_rule` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则ID 唯一标识',
    `rule_id` varchar(64) NOT NULL COMMENT '插件ID',
    `name` varchar(128) NOT NULL COMMENT '规则名称',
    `description` varchar(512) DEFAULT NULL COMMENT '规则描述',
    `level` tinyint(1) NOT NULL COMMENT '告警等级(低、中、高、严重)',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '插件状态(0启用，1禁用)',
    `type` tinyint(1) DEFAULT NULL COMMENT '规则类型(指标、日志、可用性)',
    `workspace_id` varchar(64) NOT NULL COMMENT '租户ID',
    `created_by` varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_workspace_id` (`workspace_id`),
    KEY `idx_status` (`status`),
    KEY `idx_type` (`type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则基本信息表';

CREATE TABLE `rule_plugin_mapping` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `rule_id` varchar(64) NOT NULL COMMENT '关联规则标识',
    `plugin_id` varchar(64) NOT NULL COMMENT '关联插件标识',
    `workspace_id` varchar(64) NOT NULL COMMENT '租户标识',
    `created_by` varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_rule_plugin` (`rule_id`,`plugin_id`),
    KEY `idx_plugin` (`plugin_id`),
    KEY `idx_tenant` (`workspace_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='规则与插件多对多关联关系表';


CREATE TABLE `rule_target` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '范围id',
    `rule_id` varchar(64) NOT NULL COMMENT '规则ID',
    `angle_view` tinyint(1) NOT NULL COMMENT '功能视角',
    `deploy_env` tinyint(1) NOT NULL COMMENT '部署环境',
    `workspace_id` varchar(64) NOT NULL COMMENT '租户ID',
    `created_by` varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_rule_id` (`rule_id`), -- 确保一个规则只对应一个目标范围
    KEY `idx_angle_view` (`angle_view`),
    KEY `idx_deploy_environment` (`deploy_environment`),
    KEY `idx_workspace_id` (`workspace_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则目标范围表';

-- 用inspection_rule_tags表
-- CREATE TABLE `rule_tag_mapping` (
--     `rule_id` bigint NOT NULL COMMENT '规则ID',
--     `tag_id` bigint NOT NULL COMMENT '标签ID',
--     `workspace_id` varchar(64) NOT NULL COMMENT '租户ID',
--     PRIMARY KEY (`rule_id`,`tag_id`),
--     KEY `idx_tag_id` (`tag_id`),
--     KEY `idx_workspace_id` (`workspace_id`),
--     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则标签关联表';


CREATE TABLE `rule_condition` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则执行条件ID',
    `rule_id` varchar(64) NOT NULL COMMENT '规则ID',
    `plugin_id` varchar(64) NOT NULL COMMENT '插件ID',
    `plugin_result_id` bigint NOT NULL COMMENT '监控字段（插件输出字段ID）',
    `comparison_operator` tinyint(1) NOT NULL COMMENT '判断条件(大小等于)',
    `comparison_value` decimal(15,3) NOT NULL COMMENT '数值',
    `duration` int NOT NULL COMMENT '持续时间（秒）',
    `check_interval` int DEFAULT NULL COMMENT '检查间隔（秒）',
    `check_peak` tinyint(1) DEFAULT NULL COMMENT '仅检查峰值',
    `specific_processes` int DEFAULT NULL COMMENT '检查特定进程（逗号分隔）',
    `rule_preview` varchar(255) DEFAULT NULL COMMENT '规则预览',
    `suggestion` text NOT NULL COMMENT '治理建议',
    `workspace_id` varchar(64) NOT NULL COMMENT '租户ID',
    `created_by` varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_rule_id` (`rule_id`), -- 确保一个规则对应一个执行条件
    KEY `idx_plugin_id` (`plugin_id`),
    KEY `idx_plugin_para_id` (`plugin_para_id`),
    KEY `idx_workspace_id` (`workspace_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则监控字段表';

CREATE TABLE `rule_plugin_param` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则执行条件ID',
     `rule_id` varchar(64) NOT NULL COMMENT '规则ID',
     `plugin_id` varchar(64) NOT NULL COMMENT '插件ID',
     `plugin_param_id` bigint NOT NULL COMMENT '插件参数ID',
     `plugin_param_value` varchar(64) NOT NULL COMMENT '插件参数值',
     `duration` int NOT NULL COMMENT '持续时间（秒）',
     `workspace_id` varchar(64) NOT NULL COMMENT '租户ID',
    `created_by` varchar(64) NOT NULL COMMENT '创建人',
    `created_by_name` varchar(64) NOT NULL COMMENT '创建人',
    `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_by_name` varchar(64) DEFAULT NULL COMMENT '更新人',
    `updated_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_rule_plugin_param` (`rule_id`,`plugin_id`,`plugin_param_id`), -- 复合唯一索引
    KEY `idx_rule_id` (`rule_id`),
    KEY `idx_plugin_id` (`plugin_id`),
    KEY `idx_plugin_param_id` (`plugin_param_id`),
    KEY `idx_workspace_id` (`workspace_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则插件参数配置表';
