spring:
  session:
    #登录有效期设置
    timeout: 900

lemon:
  dataSources:
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: *****************************************,***********:6446,***********:6446/hacp_prd_database?autoReconnect=true&serverTimezone=Asia/Shanghai
      username: hacpadm

hacp:
  web:
    admin:
      freeze:
        #是否开启连续90天未使用，冻结用户的功能
        enabled: true
      scim-iam:
        server:
          #单点登录服务器
          address: http://auth.4a.cmft
        #应用ID
        app-id: 1507a957-cb66-47c8-bb33-2a13e1b06354
        #应用secret
        app-secret: e8b85f9d-b583-4078-8ddc-ce5d86e327ab
        enabled-sync-user-organization: true
        enabled-organization: true
        # 启用同步任务
        enabled: true
        # 单点登录验证回调地址参数
        service: http://hacp.paycenter.pay
        sso-server: http://auth.4a.cmft/auth/cas/p3/serviceValidate
  emergence:
    cmft:
      dispatch:
        url: http://manage.dacp.cmft/strategy-admin-center-api/v3/openapi
    kubesphere:
      properties:
        url: 'http://*************:31407'
hazelcast:
  cluster-name: hzcluster-hacp

user-ssh-path: ~/.ssh/id_rsa

logging:
  level:
    #debug级别可以打印执行Sql
    org.camunda: debug
