package com.cmpay.hacp.controller.system;

import com.cmpay.framework.data.response.GenericRspDTO;
import com.cmpay.hacp.api.SystemDepartmentApi;
import com.cmpay.hacp.api.VersionApi;
import com.cmpay.hacp.bo.system.DeptBO;
import com.cmpay.hacp.dto.system.DepartmentAddReqDTO;
import com.cmpay.hacp.dto.system.DepartmentDeleteReqDTO;
import com.cmpay.hacp.dto.system.DepartmentInfoQueryRspDTO;
import com.cmpay.hacp.dto.system.DepartmentInfoRspDTO;
import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.hacp.log.annotation.LogNoneRecord;
import com.cmpay.hacp.log.annotation.LogRecord;
import com.cmpay.hacp.service.SystemDepartmentService;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api(tags = "部门管理")
@RequestMapping(VersionApi.VERSION_V1)
@RestController
public class SystemDepartmentController {

    @Autowired
    private SystemDepartmentService systemDepartmentService;

    /**
     * 查询部门信息
     *
     * @param deptId
     * @return
     */
    @ApiOperation("查询当前用户部门信息")
    @GetMapping(SystemDepartmentApi.GET_DEPARTMENT_INFO_BY_ID)
    @LogNoneRecord
    @PreAuthorize("hasPermission('SystemDepartmentController','sys:department:query')")
    public GenericRspDTO<DepartmentInfoRspDTO> getDepartmentInfoById(@PathVariable("id") String deptId) {
        DeptBO deptBO = systemDepartmentService.getDepartmentInfo(SecurityUtils.getLoginUserId(), deptId);
        DepartmentInfoRspDTO departmentInfoRspDTO = new DepartmentInfoRspDTO();
        departmentInfoRspDTO.setDeptInfo(deptBO);
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, departmentInfoRspDTO);
    }

    /**
     * 查询部门列表
     *
     * @return
     */
    @ApiOperation("查询当前用户的部门信息列表")
    @GetMapping(SystemDepartmentApi.GET_DEPARTMENT_LIST)
    @LogNoneRecord
    @PreAuthorize("hasPermission('SystemDepartmentController','sys:department:query')")
    public GenericRspDTO<DepartmentInfoQueryRspDTO> getDepartmentList() {
        DepartmentInfoQueryRspDTO departmentInfoQueryRspDTO = new DepartmentInfoQueryRspDTO();
        departmentInfoQueryRspDTO.setList(systemDepartmentService.getUserDepartments(SecurityUtils.getLoginUserId()));
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS, departmentInfoQueryRspDTO);
    }

    /**
     * 添加部门
     *
     * @param departmentAddReqDTO
     * @return
     */
    @PostMapping(SystemDepartmentApi.ADD)
    @LogRecord(title = "新增部门信息", action = "新增")
    @PreAuthorize("hasPermission('SystemDepartmentController','sys:department:save')")
    public GenericRspDTO<NoBody> add(@RequestBody DepartmentAddReqDTO departmentAddReqDTO) {
        systemDepartmentService.add(SecurityUtils.getLoginUserId(), departmentAddReqDTO.getDeptInfo());
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS);
    }

    /**
     * 修改部门
     *
     * @param departmentAddReqDTO
     * @return
     */
    @PostMapping(SystemDepartmentApi.UPDATE)
    @LogRecord(title = "修改部门信息", action = "修改")
    @PreAuthorize("hasPermission('SystemDepartmentController','sys:department:update')")
    public GenericRspDTO<NoBody> update(@RequestBody DepartmentAddReqDTO departmentAddReqDTO) {
        systemDepartmentService.update(SecurityUtils.getLoginUserId(), departmentAddReqDTO.getDeptInfo());
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS);
    }


    /**
     * 删除部门
     *
     * @param departmentDeleteReqDTO
     * @return
     */
    @DeleteMapping(SystemDepartmentApi.DELETE)
    @LogRecord(title = "删除部门信息", action = "删除")
    @PreAuthorize("hasPermission('SystemDepartmentController','sys:department:delete')")
    public GenericRspDTO<NoBody> delete(@RequestBody DepartmentDeleteReqDTO departmentDeleteReqDTO) {
        systemDepartmentService.deleteBatch(departmentDeleteReqDTO.getDeptIds());
        return GenericRspDTO.newInstance(MsgEnum.SUCCESS);
    }

}
