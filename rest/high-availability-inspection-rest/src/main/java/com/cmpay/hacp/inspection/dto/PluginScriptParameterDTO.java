package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ParamType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 插件脚本参数设置DTO
 */
@Data
@ApiModel(description = "插件脚本参数设置DTO")
public class PluginScriptParameterDTO {
    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称", required = true, example = "threshold")
    @NotBlank(message = "参数名称不能为空")
    private String paramName;

    @ApiModelProperty(value = "参数类型", required = true, example = "1")
    @NotNull(message = "参数类型不能为空")
    private ParamType paramType;

    /**
     * 正则表达式
     */
    @ApiModelProperty(value = "正则表达式", required = true, example = "^[0-9a-zA-Z.]+$")
    @NotBlank(message = "正则表达式不能为空")
    private String regexPattern;

    /**
     * 参数值示例
     */
    @ApiModelProperty(value = "参数值示例", required = true, example = "90")
    @NotBlank(message = "参数值示例不能为空")
    private String paramValue;

    /**
     * 参数描述
     */
    @ApiModelProperty(value = "参数描述", required = true, example = "告警阈值百分比")
    @NotBlank(message = "参数描述不能为空")
    private String paramDesc;

    /**
     * 是否加密(0不加密，1加密)
     */
    @ApiModelProperty(value = "是否加密：true-加密，false-不加密", example = "false")
    private Boolean isEncrypted;
}
