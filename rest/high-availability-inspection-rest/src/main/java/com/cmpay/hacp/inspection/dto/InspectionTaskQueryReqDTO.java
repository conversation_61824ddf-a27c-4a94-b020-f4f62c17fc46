package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "巡检任务查询请求DTO")
public class InspectionTaskQueryReqDTO {

    @ApiModelProperty(value = "任务ID", example = "1001")
    private String taskId;

    @ApiModelProperty(value = "任务名称", example = "CPU 使用率阈值检查")
    private String name;

    @ApiModelProperty(value = "任务描述", example = "当 CPU 使用率超过阈值时触发告警")
    private String description;

    private PageDTO<?> page;

}
