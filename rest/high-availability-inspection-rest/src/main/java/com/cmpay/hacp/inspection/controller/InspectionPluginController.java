package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.common.enums.ErrorCodeEnum;
import com.cmpay.hacp.inspection.application.service.InspectionPluginService;
import com.cmpay.hacp.inspection.application.service.TagService;
import com.cmpay.hacp.inspection.assembler.InspectionPluginDTOMapper;
import com.cmpay.hacp.inspection.assembler.TagDTOMapper;
import com.cmpay.hacp.inspection.domain.model.common.Tag;
import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;
import com.cmpay.hacp.inspection.dto.InspectionPluginQueryReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionPluginReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionPluginRspDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 巡检插件管理
 */
@RestController
@RequestMapping("/v1/inspection/plugin")
@Api(tags = "巡检插件管理")
@RequiredArgsConstructor
public class InspectionPluginController {

    private final InspectionPluginService inspectionPluginService;
    private final InspectionPluginDTOMapper inspectionPluginDTOMapper;
    private final TagService tagService;
    private final TagDTOMapper tagDTOMapper;

    /**
     * 创建巡检插件
     *
     * @param reqDTO 请求参数
     * @return 创建结果
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建巡检插件", notes = "创建新的巡检插件")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:create')")
    public DefaultRspDTO<String> createPlugin(@Validated @RequestBody InspectionPluginReqDTO reqDTO) {
        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        String pluginId = inspectionPluginService.createPlugin(inspectionPlugin);

        return DefaultRspDTO.newSuccessInstance(pluginId);
    }

    /**
     * 更新巡检插件
     *
     * @param reqDTO 请求参数
     * @return 更新结果
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新巡检插件", notes = "更新现有的巡检插件")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:update')")
    public DefaultRspDTO<NoBody> updatePlugin(@Validated @RequestBody InspectionPluginReqDTO reqDTO) {
        // 验证插件ID
        if (reqDTO.getPluginId() == null) {
            return DefaultRspDTO.newInstance(ErrorCodeEnum.PLUGIN_ID_REQUIRED);
        }

        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        // 调用服务更新插件
        boolean success = inspectionPluginService.updatePlugin(inspectionPlugin);

        if (success) {
            return DefaultRspDTO.newSuccessInstance();
        } else {
            return DefaultRspDTO.newInstance(ErrorCodeEnum.PLUGIN_UPDATE_FAILED);
        }
    }

    /**
     * 删除巡检插件
     *
     * @param pluginId 插件ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{pluginId}")
    @ApiOperation(value = "删除巡检插件", notes = "删除指定的巡检插件")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:delete')")
    public DefaultRspDTO<NoBody> deletePlugin(
            @ApiParam(name = "pluginId", value = "插件ID", required = true)
            @PathVariable("pluginId") String pluginId) {
        inspectionPluginService.deletePlugin(pluginId);

        return DefaultRspDTO.newSuccessInstance();
    }

    /**
     * 获取巡检插件详情
     *
     * @param pluginId 插件ID
     * @return 插件详情
     */
    @GetMapping("/detail/{pluginId}")
    @ApiOperation(value = "获取巡检插件详情", notes = "获取指定巡检插件的详细信息")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<InspectionPluginRspDTO> getPluginDetail(
            @ApiParam(name = "pluginId", value = "插件ID", required = true)
            @PathVariable("pluginId") String pluginId) {

        // 获取插件详情
        InspectionPlugin inspectionPlugin = inspectionPluginService.getPluginDetail(pluginId);

        List<Long> tagIds = inspectionPlugin.getTagIds();
        List<Tag> tags = tagService.getTagByTagIds(tagIds);
        InspectionPluginRspDTO rspDTO = inspectionPluginDTOMapper.toInspectionPluginRspDTO(inspectionPlugin);
        rspDTO.setTags(tagDTOMapper.toTagDTOList(tags));

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

    /**
     * 分页查询巡检插件列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询巡检插件列表", notes = "根据条件分页查询巡检插件列表")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('InspectionPluginController','inspection:plugin:query')")
    public DefaultRspDTO<PageDTO<InspectionPluginRspDTO>> getPluginPage(@Validated @RequestBody InspectionPluginQueryReqDTO reqDTO) {
        InspectionPlugin inspectionPlugin = inspectionPluginDTOMapper.toInspectionPlugin(reqDTO);

        IPage<InspectionPlugin> page = inspectionPluginService.getPluginPage(reqDTO.getPage(), inspectionPlugin);
        List<InspectionPluginRspDTO> rspDTOList = inspectionPluginDTOMapper.toInspectionPluginRspDTOList(page.getRecords());
        PageDTO<InspectionPluginRspDTO> result = new PageDTO<>(page.getCurrent(), page.getSize(), page.getTotal());
        result.setRecords(rspDTOList);

        return DefaultRspDTO.newSuccessInstance(result);
    }
}
