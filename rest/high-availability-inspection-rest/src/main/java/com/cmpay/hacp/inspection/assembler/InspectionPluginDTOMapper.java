package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.plugin.InspectionPlugin;
import com.cmpay.hacp.inspection.dto.InspectionPluginQueryReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionPluginReqDTO;
import com.cmpay.hacp.inspection.dto.InspectionPluginRspDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 巡检插件对象转换器
 */
@Mapper(componentModel = "spring")
public interface InspectionPluginDTOMapper {

    @Mapping(target = "auditInfo", ignore = true)
    InspectionPlugin toInspectionPlugin(InspectionPluginReqDTO reqDTO);

    @Mapping(target = "tags", ignore = true)
    InspectionPluginRspDTO toInspectionPluginRspDTO(InspectionPlugin inspectionPlugin);

    @Mapping(target = "tagIds", ignore = true)
    @Mapping(target = "scriptResultType", ignore = true)
    @Mapping(target = "scriptContent", ignore = true)
    @Mapping(target = "results", ignore = true)
    @Mapping(target = "parameters", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    InspectionPlugin toInspectionPlugin(InspectionPluginQueryReqDTO reqDTO);

    List<InspectionPluginRspDTO> toInspectionPluginRspDTOList(List<InspectionPlugin> inspectionPluginList);
}
