package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.RuleCheckPeakEnum;
import com.cmpay.hacp.inspection.domain.model.enums.RuleComparisonOperator;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 规则监控字段DTO
 */
@Data
@ApiModel(description = "规则监控字段DTO")
public class RulePluginResultDTO {

    @ApiModelProperty(value = "插件ID", required = true, example = "1")
    @NotBlank(message = "插件ID不能为空")
    private String pluginId;

    @ApiModelProperty(value = "监控字段（插件输出字段ID）", required = true, example = "1")
    @NotNull(message = "监控字段不能为空")
    private Long pluginResultId;

    /**
     * @see RuleComparisonOperator
     */
    @ApiModelProperty(value = "判断条件(1大小2小于3大于等于4小于等于5等于)", required = true, example = "1")
    @NotNull(message = "判断条件不能为空")
    private RuleComparisonOperator judge;

    @ApiModelProperty(value = "数值", required = true, example = "100")
    @NotNull(message = "判断数值不能为空")
    private BigDecimal judgeValue;

    @ApiModelProperty(value = "持续时间（秒）", required = true, example = "300")
    @NotNull(message = "持续时间不能为空")
    private Integer duration;

    @ApiModelProperty(value = "检查间隔（秒）", required = true, example = "60")
    @NotNull(message = "检查间隔不能为空")
    private Integer checkInterval;

    /**
     * @see RuleCheckPeakEnum
     */
    @ApiModelProperty(value = "仅检查峰值（0-否，1-是）", required = true,example = "0")
    @NotNull(message = "仅检查峰值不能为空,不勾选时传0")
    private RuleCheckPeakEnum checkPeak;

    @ApiModelProperty(value = "检查特定进程（逗号分隔）",example = "java,mysql,nginx")
    private Integer specificProcesses;

    @ApiModelProperty(value = "规则预览",example = "当 connection_count > 持续 300 秒，每 60 秒检查一次 时触发告警")
    private String rulePreview;

    @ApiModelProperty(value = "治理建议", required = true,example = "请输入当规则触发时的治理建议")
    @NotBlank(message = "治理建议不能为空")
    private String suggest;

}
