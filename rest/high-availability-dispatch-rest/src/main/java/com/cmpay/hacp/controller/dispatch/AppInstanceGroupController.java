package com.cmpay.hacp.controller.dispatch;

import com.cmpay.hacp.controller.dto.AppInstanceGroupPageableReqDTO;
import com.cmpay.hacp.controller.dto.AppInstanceGroupReqDTO;
import com.cmpay.hacp.dispatch.bo.AppInstanceGroupBO;
import com.cmpay.hacp.dispatch.bo.AppInstanceMachineBO;
import com.cmpay.hacp.dispatch.service.AppInstanceGroupService;
import com.cmpay.hacp.log.annotation.LogNoneRecord;
import com.cmpay.hacp.log.annotation.LogRecord;
import com.cmpay.hacp.utils.TenantSecurityUtils;
import com.cmpay.hacp.utils.TenantUtils;
import com.cmpay.hacp.utils.bean.BeanConvertUtil;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.data.NoBody;
import com.cmpay.lemon.framework.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/app-instance-group")
@Api(tags = "业务节点组")
public class AppInstanceGroupController {

    @Autowired
    private AppInstanceGroupService appInstanceGroupService;

    @ApiOperation(value = "add", notes = "添加业务节点组", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/add")
    @LogRecord(title = "添加业务节点组", action = "新增")
    @PreAuthorize("hasPermission('ApiTagController','dispatch:instance-group:add')")
    public DefaultRspDTO<NoBody> add(@RequestBody AppInstanceGroupReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceGroupService.addAppInstanceGroup(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "page", notes = "获取业务节点组分页列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/page")
    @LogNoneRecord
    @PreAuthorize("hasPermission('ApiTagController','dispatch:instance-group:query')")
    public DefaultRspDTO<PageInfo<AppInstanceMachineBO>> getPage(AppInstanceGroupPageableReqDTO reqDTO) {
        Optional.ofNullable(TenantUtils.getWorkspaceId()).ifPresent(workspaceId -> reqDTO.setWorkspaceId(workspaceId));
        PageInfo<AppInstanceMachineBO> pageInfo = appInstanceGroupService.getInstanceGroupMachineList(reqDTO.getPageNum(), reqDTO.getPageSize(), BeanConvertUtil.convert(reqDTO, AppInstanceMachineBO.class));
        if(pageInfo.getList() != null) {
            for (AppInstanceMachineBO machineBO : pageInfo.getList()) {
                machineBO.convertToZone();
            }
        }
        return DefaultRspDTO.newSuccessInstance(pageInfo);
    }

    @ApiOperation(value = "list-all", notes = "获取业务节点组列表", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @GetMapping("/list")
    @LogNoneRecord
    public DefaultRspDTO<List<AppInstanceMachineBO>> getList() {
        List<AppInstanceMachineBO> list = appInstanceGroupService.getInstanceGroupAllList(TenantUtils.getWorkspaceId());
        return DefaultRspDTO.newSuccessInstance(list);
    }

    @ApiOperation(value = "delete", notes = "删除业务节点组", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @PostMapping("/delete")
    @LogRecord(title = "删除业务节点组", action = "删除")
    @PreAuthorize("hasPermission('ApiTagController','dispatch:instance-group:delete')")
    public DefaultRspDTO<NoBody> delete(@RequestBody AppInstanceGroupReqDTO reqDTO) {
        TenantSecurityUtils.copyOperator(reqDTO);
        appInstanceGroupService.deleteAppInstanceGroup(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "update", notes = "更新业务节点组", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "修改业务节点组", action = "修改")
    @PostMapping("/update")
    @PreAuthorize("hasPermission('ApiTagController','dispatch:instance-group:update')")
    public DefaultRspDTO<NoBody> update(@RequestBody AppInstanceGroupReqDTO reqDTO) {
        TenantSecurityUtils.copyTenantSecurity(reqDTO);
        appInstanceGroupService.updateAppInstanceGroup(reqDTO);
        return DefaultRspDTO.newSuccessInstance();
    }

    @ApiOperation(value = "get", notes = "获取业务节点组详细信息", produces = "application/json")
    @ApiResponse(code = 200, message = "成功")
    @LogRecord(title = "查询业务节点组详情", action = "查询")
    @GetMapping("/details")
    @PreAuthorize("hasPermission('ApiTagController','dispatch:instance-group:info')")
    public DefaultRspDTO<AppInstanceGroupBO> get(AppInstanceGroupReqDTO reqDTO) {
        TenantSecurityUtils.copyOperator(reqDTO);
        AppInstanceGroupBO appInstanceGroup = appInstanceGroupService.getAppInstanceGroup(reqDTO);
        return DefaultRspDTO.newSuccessInstance(appInstanceGroup);
    }
}