pluginManagement {
    repositories {
        maven { 
            url 'http://nexus.devops.cmft/repository/maven-public/'
            allowInsecureProtocol = true
        }
        gradlePluginPortal()
    }
}

rootProject.name = 'hacp'
include ':app:high-availability-control-platform-app'
include ':app:high-availability-automated-inspection-app'
include ':common:high-availability-common'


include ':rest:high-availability-dispatch-rest'
include ':rest:high-availability-dashboard-rest'
include ':rest:high-availability-system-rest'
include ':rest:high-availability-tenant-rest'
include ':rest:high-availability-message-rest'
include ':rest:high-availability-emergency-rest'
include ':rest:high-availability-inspection-rest'

include ':service:high-availability-emergency-service'
include ':service:high-availability-system-service'
include ':service:high-availability-tenant-service'
include ':service:high-availability-dispatch-service'
include ':service:high-availability-dashboard-service'
include ':service:high-availability-message-service'
include ':service:high-availability-inspection-service'

include ':interface:high-availability-base-interface'

include ':rest:high-availability-extenal-rest:high-availability-container-rest'
include ':rest:high-availability-extenal-rest:high-availability-cmft-rest'
include ':rest:high-availability-extenal-rest:high-availability-sso-rest'

include ':service:high-availability-extenal-service:high-availability-container-service'
include ':service:high-availability-extenal-service:high-availability-cmft-service'
include ':service:high-availability-extenal-service:high-availability-sso-service'

include ':autoconfigure:high-availability-extenal-autoconfigure:high-availability-sso-autoconfigure'
